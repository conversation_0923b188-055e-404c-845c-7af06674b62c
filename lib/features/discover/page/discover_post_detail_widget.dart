import 'dart:async';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/home_feed_screen/model/post_response_model.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/comment_bottom_sheet_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class DiscoverUserPostListScreen extends StatefulWidget {
  final List<PostData>? discoverfpost;
  final int? initialIndex;
  const DiscoverUserPostListScreen({super.key, this.discoverfpost, this.initialIndex});

  static Widget builder(BuildContext context) {
    return const DiscoverUserPostListScreen();
  }

  @override
  State<DiscoverUserPostListScreen> createState() => _DiscoverUserPostListScreenState();
}

class _DiscoverUserPostListScreenState extends State<DiscoverUserPostListScreen> with SingleTickerProviderStateMixin {
  late ItemScrollController _itemScrollController;
  late ItemPositionsListener _itemPositionsListener;
  int _initialIndex = 0;
  final int _threshold = 3; // Reduced threshold for better trigger

  // Add cache for posts
  final Map<int, Widget> _postCache = {};
  bool _isLoadingMore = false;
  bool _hasTriggeredLoad = false; // Prevent multiple simultaneous loads

  // Offline message state
  Timer? _offlineMessageTimer;
  bool _showOfflineMessage = false;
  bool _isRefreshAttemptedOffline = false;

  @override
  void initState() {
    super.initState();
    _itemScrollController = ItemScrollController();
    _itemPositionsListener = ItemPositionsListener.create();
    _initialIndex = widget.initialIndex ?? 0;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_initialIndex >= 0 && _initialIndex < (widget.discoverfpost?.length ?? 0)) {
        _scrollToInitialIndex();
      }
    });
    _itemPositionsListener.itemPositions.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _itemPositionsListener.itemPositions.removeListener(_scrollListener);
    super.dispose();
  }

  void _scrollToInitialIndex() {
    _itemScrollController.jumpTo(
      index: _initialIndex,
    );
  }

  Future<void> _refreshFeed() async {
    final state = context.read<HomeFeedBloc>().state;
    final connectivityState = context.read<ConnectivityBloc>().state;

    if (!connectivityState.isConnected) {
      // User attempted to refresh while offline
      _isRefreshAttemptedOffline = true;
      _offlineMessageTimer?.cancel();
      _offlineMessageTimer = Timer(const Duration(seconds: 5), () {
        if (mounted && _isRefreshAttemptedOffline) {
          setState(() {
            _showOfflineMessage = true;
          });
          Timer(const Duration(seconds: 3), () {
            if (mounted) {
              setState(() {
                _showOfflineMessage = false;
                _isRefreshAttemptedOffline = false;
              });
            }
          });
        }
      });
      return;
    }

    if (state.isDiscoverposts.isNotEmpty) {
      state.isDiscoverposts.clear();
    }

    // Clear cache and reset flags on refresh
    _postCache.clear();
    _isLoadingMore = false;
    _hasTriggeredLoad = false;

    scheduleMicrotask(() => context.read<HomeFeedBloc>().add(DiscoverPostApiEvent(page: 1)));
  }

  void _scrollListener() {
    // Prevent multiple simultaneous loads
    if (_isLoadingMore || _hasTriggeredLoad) return;

    final visibleItems = _itemPositionsListener.itemPositions.value;
    if (visibleItems.isEmpty) return;

    final state = context.read<HomeFeedBloc>().state;
    final totalPosts = state.isDiscoverposts.length;

    // Check if we have posts and pagination is available
    if (totalPosts == 0) return;

    // Get the last visible item index
    final lastVisibleIndex = visibleItems.map((item) => item.index).reduce((a, b) => a > b ? a : b);

    // Trigger pagination when approaching the end
    final shouldLoadMore = lastVisibleIndex >= totalPosts - _threshold;

    if (shouldLoadMore) {
      // Check if there's a next page available
      final hasNextPage = state.getAllPostModel?.nextPage != null;
      final isNotCurrentlyLoading = !state.isDiscoverLoadingMore;

      if (hasNextPage && isNotCurrentlyLoading) {
        _hasTriggeredLoad = true;
        _isLoadingMore = true;

        context.read<HomeFeedBloc>().add(DiscoverPostApiEvent(page: state.isDiscoverpage + 1));

        // Reset the trigger flag after a delay to prevent rapid firing
        Timer(const Duration(milliseconds: 500), () {
          _hasTriggeredLoad = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Scaffold(
          appBar: CustomAppbar(
            hasLeadingIcon: true,
            height: 18.h,
            leading: [
              InkWell(
                onTap: () {
                  PersistentNavBarNavigator.pop(context);
                },
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: CustomImageView(
                    imagePath: Assets.images.svg.authentication.icBackArrow.path,
                    height: 16.h,
                  ),
                ),
              ),
              buildSizedBoxW(20.w),
              Text(
                Lang.of(context).lbl_explore,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
              ),
            ],
          ),
          body: BlocListener<ConnectivityBloc, ConnectivityState>(
            listener: (context, state) {
              if (state.isReconnected) {
                if (context.read<HomeFeedBloc>().state.isDiscoverposts.isEmpty) {
                  _refreshFeed();
                }
              }
            },
            child: BlocBuilder<ThemeBloc, ThemeState>(
              builder: (context, themestate) {
                return BlocBuilder<HomeFeedBloc, HomeFeedState>(
                  builder: (context, state) {
                    // Reset loading flag when loading completes
                    if (!state.isDiscoverLoadingMore && _isLoadingMore) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        _isLoadingMore = false;
                      });
                    }

                    return Scaffold(
                      body: BlocBuilder<ConnectivityBloc, ConnectivityState>(
                        builder: (context, connectivityState) {
                          return Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Expanded(child: _buildDiscoverPost(state, themestate)),
                              // Loading indicator at bottom
                              if (state.isDiscoverLoadingMore && connectivityState.isConnected)
                                Container(
                                  height: 50.h,
                                  color: Colors.transparent,
                                  child: Center(
                                    child: CupertinoActivityIndicator(
                                      color: Theme.of(context).colorScheme.primary,
                                    ),
                                  ),
                                ),
                            ],
                          );
                        },
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ),
        _buildOfflineMessage(_showOfflineMessage),
      ],
    );
  }

  Widget _buildDiscoverPost(HomeFeedState state, ThemeState themestate) {
    return LiquidPullToRefresh(
      color: Theme.of(context).primaryColor.withOpacity(0.5),
      showChildOpacityTransition: false,
      backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      onRefresh: _refreshFeed,
      child: BlocBuilder<ConnectivityBloc, ConnectivityState>(
        builder: (context, connectivityState) {
          if (!connectivityState.isConnected && state.isDiscoverposts.isEmpty) {
            return HomeFeedShimmer();
          } else if (state.isdiscoverloding && state.isDiscoverposts.isEmpty) {
            return HomeFeedShimmer();
          } else if (state.isDiscoverposts.isEmpty) {
            return ListView(
              physics: AlwaysScrollableScrollPhysics(),
              children: [
                buildSizedBoxH(MediaQuery.of(context).size.height / 6),
                ExceptionWidget(
                  imagePath: Assets.images.svg.exception.svgNodatafound.path,
                  showButton: false,
                  title: Lang.of(context).lbl_no_data_found,
                  subtitle: Lang.of(context).lbl_no_post,
                ),
              ],
            );
          } else {
            return ScrollablePositionedList.builder(
              padding: EdgeInsets.only(bottom: 70.h, top: 8.h),
              itemCount: state.isDiscoverposts.length,
              itemScrollController: _itemScrollController,
              itemPositionsListener: _itemPositionsListener,
              physics: const ClampingScrollPhysics(),
              itemBuilder: (context, index) {
                final post = state.isDiscoverposts[index];

                // Use selective caching - don't cache too many items
                Widget? cachedWidget;
                if (_postCache.length < 10) {
                  // Limit cache size
                  cachedWidget = _postCache[post.id];
                }

                if (cachedWidget != null) {
                  return cachedWidget;
                }

                final postWidget = RepaintBoundary(
                  child: PostWidget(
                    key: ValueKey('discover_post_${post.id}_$index'),
                    width: post.width,
                    height: post.height,
                    userByIDpost: false,
                    userByIDvideo: false,
                    userVideo: false,
                    userpost: false,
                    isDiscoverPosts: true,
                    isTextPost: post.isTextPost,
                    taggedIn: post.taggedIn,
                    state: state,
                    latestcomments: post.latestComment.toString(),
                    index: index,
                    userId: post.user.userId,
                    postId: post.id,
                    profileImage: post.user.profileImage,
                    name: post.user.name,
                    username: post.user.username,
                    postMedia: post.files,
                    thumbnailImage: post.thumbnailFiles?.isEmpty ?? true ? [] : post.thumbnailFiles ?? [],
                    title: post.title == "''" || post.title.isEmpty ? '' : post.title,
                    caption:
                        "${post.title == "''" || post.title.isEmpty ? '' : post.title}${post.description == '' || post.description.isEmpty ? '' : post.title == "''" || post.title.isEmpty ? post.description : "\n${post.description}"}",
                    likes: post.likes.toString(),
                    comments: post.commentsCount.toString(),
                    postTime: post.createdAt,
                    isLiked: post.isLiked,
                    isSaved: post.isSaved,
                    screenType: "User Profile",
                    reelScreenType: 'Discover',
                    doubleTap: () {
                      if (post.isLiked == false) {
                        context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: post.id));
                      }
                    },
                    likeonTap: () {
                      context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: post.id));
                    },
                    commentonTap: () {
                      showModalBottomSheet(
                        context: context,
                        useRootNavigator: true,
                        isScrollControlled: true,
                        builder: (context) => CommentsBottomSheet(postId: post.id),
                      );
                    },
                    shareonTap: () {},
                    saveonTap: () {},
                  ),
                );

                // Cache the widget if cache isn't full
                if (_postCache.length < 10) {
                  _postCache[post.id] = postWidget;
                }

                return postWidget;
              },
            );
          }
        },
      ),
    );
  }

  Widget _buildOfflineMessage(bool showOfflineMessage) {
    return Positioned(
      bottom: 20.h,
      left: 0,
      right: 0,
      child: AnimatedSlide(
        offset: _showOfflineMessage ? Offset.zero : const Offset(0, 1),
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
        child: AnimatedOpacity(
          opacity: _showOfflineMessage ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 300),
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 10.w),
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.95),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.wifi_off,
                  color: Theme.of(context).customColors.white,
                  size: 18.sp,
                ),
                buildSizedBoxW(8),
                Text(
                  "Couldn't refresh feed",
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Theme.of(context).customColors.white,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
