import 'dart:developer';
import 'dart:ui';
import 'dart:async';
import 'package:flowkar/core/helpers/vibration_helper.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/home_feed_screen/model/post_response_model.dart';
import 'package:flowkar/features/home_feed_screen/model/video_response_model.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/comment_bottom_sheet_widget.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/flick_multi_manager.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/share_post_bottomsheet.dart';
import 'package:flowkar/features/survey_form/bloc/survey_bloc.dart';
import 'package:flowkar/features/widgets/common/get_user_profile_by_id/get_user_profile_by_id.dart';
import 'package:video_player/video_player.dart';
import 'package:visibility_detector/visibility_detector.dart';
import '../../config/cache_config.dart';
import '../../service/reel_service.dart' as reel_service;

class VideoPlayerWidget extends StatefulWidget {
  final String reelUrl;
  final reel_service.ReelData? reelData;
  final bool isActive;
  final reel_service.ReelService? reelService;

  const VideoPlayerWidget({
    super.key,
    required this.isActive,
    required this.reelUrl,
    this.reelData,
    this.reelService,
  });

  @override
  State<VideoPlayerWidget> createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget>
    with WidgetsBindingObserver, SingleTickerProviderStateMixin {
  VideoPlayerController? _controller;
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isExpanded = false;
  bool _isAnimationVisible = false;
  bool _videoInitialized = false;
  bool _isPlaying = false;
  bool _isMuted = false;
  bool _isDisposed = false;
  bool _isInitializing = false;
  Timer? _initializationTimer;
  String currentUserId = '';
  late bool isPostPermission;
  int index = 0;
  late bool isBlockPermission;

  @override
  void initState() {
    super.initState();
    // _isMuted = false;
    context.read<HomeFeedBloc>().add(FetchUserProfilePostEvent(page: 1));
    context.read<HomeFeedBloc>().add(FetchUserProfileVideoEvent(page: 1));

    currentUserId = Prefobj.preferences!.get(Prefkeys.USER_ID).toString();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    )..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          _animationController.reverse();
        }
      });
    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );
    WidgetsBinding.instance.addObserver(this);
    reel_service.GlobalVideoManager().stopAllExcept(widget.reelUrl);
    _initializationTimer = Timer(const Duration(milliseconds: 100), () {
      if (!_isDisposed) {
        initializeController();
      }
    });

    widget.reelData?.addListener(() {
      if (mounted && !_isDisposed) {
        setState(() {});
      }
    });
  }

  @override
  void didUpdateWidget(covariant VideoPlayerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!_isDisposed && _controller?.value.isInitialized == true) {
      if (widget.isActive && reel_service.GlobalVideoManager().canPlayVideo(widget.reelUrl)) {
        _playVideo();
        _controller!.setVolume(_isMuted ? 0.0 : 1.0);
      } else {
        _pauseVideo();
        _controller!.setVolume(0.0);
      }
    }
  }

  Future<void> initializeController() async {
    if (_isDisposed || _isInitializing) return;

    _isInitializing = true;

    try {
      var fileInfo = await kCacheManager.getFileFromCache(widget.reelUrl);
      if (fileInfo == null) {
        await kCacheManager.downloadFile(widget.reelUrl);
        fileInfo = await kCacheManager.getFileFromCache(widget.reelUrl);
      }

      if (_isDisposed || !mounted) return;

      await _disposeController();

      _controller = VideoPlayerController.file(fileInfo!.file);

      await _controller!.initialize();

      if (_isDisposed || !mounted) {
        await _disposeController();
        return;
      }

      _controller!.setLooping(true);
      _isMuted = GlobalSetting.isUserMuted;
      _controller!.setVolume(_isMuted ? 0.0 : 1.0);

      _controller!.addListener(_onVideoStateChanged);

      reel_service.GlobalVideoManager().registerVideo(widget.reelUrl);
      _controller!.setVolume(_isMuted ? 0.0 : 1.0);
      setState(() {
        _videoInitialized = true;
        _isPlaying = false;
      });

      await _controller!.play();
    } catch (e) {
      log('Error initializing video controller: $e');
      if (mounted && !_isDisposed) {
        setState(() {
          _videoInitialized = false;
        });
      }
    } finally {
      _isInitializing = false;
    }
  }

  void _onVideoStateChanged() {
    if (_isDisposed || !mounted) return;

    if (_controller?.value.isPlaying == true && !_isPlaying) {
      setState(() {
        _isPlaying = true;
        _controller!.setVolume(_isMuted ? 0.0 : 1.0);
      });

      reel_service.GlobalVideoManager().setCurrentlyPlaying(widget.reelUrl);
    } else if (_controller?.value.isPlaying == false && _isPlaying) {
      setState(() {
        _isPlaying = false;
        _controller!.setVolume(_isMuted ? 0.0 : 1.0);
      });

      if (reel_service.GlobalVideoManager().isCurrentlyPlaying(widget.reelUrl)) {
        reel_service.GlobalVideoManager().stopAllVideos();
      }
    }
  }

  Future<void> _disposeController() async {
    if (_controller != null) {
      try {
        _controller!.removeListener(_onVideoStateChanged);
        if (_controller!.value.isInitialized) {
          await _controller!.pause();
          await _controller!.setVolume(0.0);
        }
        await _controller!.dispose();
      } catch (e) {
        log('Error disposing controller: $e');
      } finally {
        _controller = null;
      }
    }

    reel_service.GlobalVideoManager().unregisterVideo(widget.reelUrl);
  }

  void _pauseVideo() {
    if (_controller?.value.isInitialized == true) {
      _controller!.pause();
      _controller!.setVolume(0.0);
    }
  }

  void _toggleMute() {
    setState(() {
      _isMuted = !_isMuted;
      GlobalSetting.isUserMuted = _isMuted;
      _controller?.setVolume(_isMuted ? 0.0 : 1.0);
      Logger.lOG('Mute state: $_isMuted, Volume: ${_controller!.value.volume}');
    });
  }

  void _playVideo() {
    if (_controller?.value.isInitialized == true) {
      _controller!.play();
      _controller!.setVolume(_isMuted ? 0.0 : 1.0);
      reel_service.GlobalVideoManager().setCurrentlyPlaying(widget.reelUrl);
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (_isDisposed || _controller == null) return;

    switch (state) {
      case AppLifecycleState.resumed:
        // Resume only if this is the allowed one

        if (_videoInitialized &&
            reel_service.GlobalVideoManager().canPlayVideo(widget.reelUrl) &&
            reel_service.GlobalVideoManager().isCurrentlyPlaying(widget.reelUrl)) {
          _playVideo();
        }
        break;

      case AppLifecycleState.paused:
        _pauseVideo();
      case AppLifecycleState.inactive:
        if (_videoInitialized &&
            reel_service.GlobalVideoManager().canPlayVideo(widget.reelUrl) &&
            reel_service.GlobalVideoManager().isCurrentlyPlaying(widget.reelUrl)) {
          _playVideo();
        }
      case AppLifecycleState.hidden:
        // Pause only when app is backgrounded or hidden
        _pauseVideo();
        break;

      case AppLifecycleState.detached:
        // Full cleanup
        _disposeController();
        break;
    }
  }

  void _onDoubleTap() {
    context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
    if (_animationController.isAnimating) {
      _animationController.stop();
    }
    _animationController.reset();
    _animationController.forward();
    setState(() {
      _isAnimationVisible = true;
    });
  }

  @override
  void dispose() {
    _isDisposed = true;
    _initializationTimer?.cancel();
    widget.reelData?.removeListener(() {});
    _animationController.dispose();
    _disposeController();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    isPostPermission = Prefobj.preferences?.get(Prefkeys.PRM_POST) ?? false;
    isBlockPermission = Prefobj.preferences?.get(Prefkeys.PRM_BLOCK_UNBLOCK) ?? false;
    return SafeArea(
      bottom: true,
      top: false,
      left: false,
      right: false,
      child: VisibilityDetector(
        key: Key('video_${widget.reelUrl}'),
        onVisibilityChanged: (visibilityInfo) {
          if (_isDisposed) return;

          if (visibilityInfo.visibleFraction == 0) {
            _pauseVideo();
          } else if (visibilityInfo.visibleFraction > 0.5) {
            if (_videoInitialized) {
              _playVideo();
            }
          }
        },
        child: GestureDetector(
          onTap: () {
            if (_videoInitialized && _controller?.value.isInitialized == true) {
              setState(() {
                if (_controller!.value.isPlaying) {
                  _controller!.pause();
                  _isPlaying = false;
                } else {
                  _controller!.play();
                  _isPlaying = true;
                }
              });
              Logger.lOG("Paletefhe $isMute");
            }
          },
          onDoubleTap: () {
            _onDoubleTap();
            if (!(widget.reelData?.isLiked ?? false)) {
              widget.reelData?.setLikeState(true);
            }
            context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: widget.reelData?.id ?? 0));
            context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
            Logger.lOG("like${widget.reelData?.likes}");
          },
          child: Stack(
            alignment: AlignmentDirectional.center,
            children: [
              !_videoInitialized
                  ? const Center(child: LoadingAnimationWidget())
                  : SizedBox.expand(
                      child: FittedBox(
                        fit: BoxFit.cover,
                        child: SizedBox(
                          width: _controller?.value.size.width ?? 0,
                          height: _controller?.value.size.height ?? 0,
                          child: _controller != null ? VideoPlayer(_controller!) : const SizedBox(),
                        ),
                      ),
                    ),

              if (_videoInitialized) _buildPlayPauseButton(),

              // buildSizedBoxH(10.h),
              // if (_videoInitialized) _buildMuteUnmutedButton(),
              !_videoInitialized
                  ? const SizedBox()
                  : Align(
                      alignment: Alignment.bottomCenter,
                      child: VideoProgressIndicator(
                        _controller!,
                        allowScrubbing: true,
                        colors: VideoProgressColors(
                          playedColor: Theme.of(context).primaryColor,
                          bufferedColor: Theme.of(context).dividerColor,
                          backgroundColor: Theme.of(context).colorScheme.surface,
                        ),
                      ),
                    ),
              _buildGradientOverlay(),
              if (_isAnimationVisible)
                AnimatedBuilder(
                  animation: _animation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _animation.value,
                      child: Icon(
                        Icons.favorite_rounded,
                        size: 100.h,
                        // ignore: deprecated_member_use
                        color: Colors.red.withOpacity(0.7),
                      ),
                    );
                  },
                ),
              _buildUserInfo(widget.reelData),
              if (_videoInitialized)
                Align(
                  alignment: Alignment.center,
                  child: Padding(
                    padding: EdgeInsets.only(
                      bottom: 100.h,
                    ),
                    child: (_isPlaying)
                        ? SizedBox.shrink()
                        : GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: () {
                              setState(() {
                                _toggleMute();
                                _controller!.play();
                              });
                              Logger.lOG("Mute button tapped");
                            },
                            child: _buildMuteUnmutedButton(),
                          ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGradientOverlay() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.transparent,
            // ignore: deprecated_member_use
            Theme.of(context).customColors.black.withOpacity(0.5),
          ],
        ),
      ),
    );
  }

  Widget _buildMuteUnmutedButton() {
    return Opacity(
      opacity: _isPlaying ? 0 : 1,
      child: ClipOval(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            height: 35,
            width: 35,
            decoration: BoxDecoration(
              color: Theme.of(context).customColors.black.withOpacity(0.4),
              shape: BoxShape.circle,
            ),
            alignment: Alignment.center,
            child: Icon(
              _isMuted ? Icons.volume_off : Icons.volume_up,
              color: Theme.of(context).customColors.white,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlayPauseButton() {
    return Opacity(
      opacity: _isPlaying ? 0 : 1,
      child: Align(
        alignment: Alignment.center,
        child: ClipOval(
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              height: 50,
              width: 50,
              decoration: BoxDecoration(
                // ignore: deprecated_member_use
                color: Theme.of(context).customColors.black.withOpacity(0.4),
                shape: BoxShape.circle,
              ),
              alignment: Alignment.center,
              child: Icon(
                _isPlaying ? Icons.pause : Icons.play_arrow,
                color: Theme.of(context).customColors.white,
                size: 30,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfo(reel_service.ReelData? reelData) {
    return Align(
      alignment: Alignment.bottomLeft,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  InkWell(
                    onTap: () {
                      if (widget.reelData?.user.userId.toString() == currentUserId) {
                        PersistentNavBarNavigator.pushNewScreen(context,
                            screen: UserProfileScreen(), withNavBar: false);
                        reel_service.GlobalVideoManager().stopAllVideos();
                        _controller?.setVolume(0.0);
                        _pauseVideo();
                      } else {
                        PersistentNavBarNavigator.pushNewScreen(context,
                            screen: GetUserProfileById(
                              userId: widget.reelData?.user.userId,
                              stackonScreen: false,
                              // key: ValueKey("profile_user_${widget.reelData?.user.userId}"),
                            ),
                            withNavBar: false);
                        reel_service.GlobalVideoManager().stopAllVideos();
                        _pauseVideo();
                        _controller?.setVolume(0.0);
                      }
                    },
                    child: Row(
                      children: [
                        Container(
                          height: 32.h,
                          width: 32.w,
                          padding: (reelData != null && reelData.user.profileImage.isNotEmpty)
                              ? EdgeInsets.zero
                              : (reelData != null &&
                                      reelData.user.userId.toString() ==
                                          Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                                  ? EdgeInsets.all(6)
                                  : EdgeInsets.zero),
                          decoration: BoxDecoration(
                            color: (reelData != null && reelData.user.profileImage.isNotEmpty)
                                ? Theme.of(context).primaryColor.withOpacity(0.3)
                                : (reelData != null &&
                                        reelData.user.userId.toString() ==
                                            Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                                    ? Theme.of(context).customColors.white
                                    : Theme.of(context).primaryColor.withOpacity(0.3)),
                            borderRadius: (reelData != null && reelData.user.profileImage.isNotEmpty)
                                ? BorderRadius.circular(100.r)
                                : (reelData != null &&
                                        reelData.user.userId.toString() ==
                                            Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                                    ? BorderRadius.zero
                                    : BorderRadius.circular(100.r)),
                          ),
                          child: CustomImageView(
                            radius: BorderRadius.circular(100.r),
                            imagePath: (reelData != null && reelData.user.profileImage.isNotEmpty)
                                ? reelData.user.profileImage
                                : (reelData != null &&
                                        reelData.user.userId.toString() ==
                                            Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                                    ? AssetConstants.pngUser
                                    : AssetConstants.pngUserReomve),
                            fit: (reelData != null && reelData.user.profileImage.isNotEmpty)
                                ? BoxFit.cover
                                : (reelData != null &&
                                        reelData.user.userId.toString() ==
                                            Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                                    ? BoxFit.contain
                                    : BoxFit.cover),
                          ),
                        ),
                        buildSizedBoxW(12),
                        Expanded(
                          child: Text(
                            reelData?.user.name ?? '',
                            maxLines: 1,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Theme.of(context).customColors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14.sp,
                                  overflow: TextOverflow.ellipsis,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (reelData?.title != "''") buildSizedBoxH(12),
                  if (reelData?.title != "''")
                    Text(
                      reelData?.title == "''" ? '' : reelData?.title ?? '',
                      maxLines: _isExpanded ? null : 2,
                      overflow: _isExpanded ? null : TextOverflow.ellipsis,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).customColors.white,
                            fontSize: 13.sp,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  if (reelData?.title == "''") buildSizedBoxH(6),
                  if (reelData?.description.isNotEmpty ?? false) buildSizedBoxH(4),
                  if (reelData?.description.isNotEmpty ?? false)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ConstrainedBox(
                          constraints: BoxConstraints(
                            maxHeight: _isExpanded ? 300.h : 50.h,
                          ),
                          child: SingleChildScrollView(
                            physics: _isExpanded
                                ? const AlwaysScrollableScrollPhysics()
                                : const NeverScrollableScrollPhysics(),
                            child: Text(
                              reelData?.description ?? '',
                              maxLines: _isExpanded ? null : 2,
                              overflow: _isExpanded ? null : TextOverflow.ellipsis,
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Theme.of(context).customColors.white,
                                    fontSize: 13.sp,
                                  ),
                            ),
                          ),
                        ),
                        if ((reelData!.title.length > 50 || reelData.description.length > 50))
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                _isExpanded = !_isExpanded;
                              });
                            },
                            child: Padding(
                              padding: const EdgeInsets.only(top: 4),
                              child: Text(
                                _isExpanded ? 'See Less' : 'See More',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      // ignore: deprecated_member_use
                                      color: Theme.of(context).customColors.white.withOpacity(0.7),
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  buildSizedBoxH(16),
                ],
              ),
            ),
            buildSizedBoxW(30),
            Column(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                //////////////////////////////////////Here Change/////////////////////////////////////////////////
                _buildActionButton(
                  context,
                  count: reelData?.likes,
                  iconPath: reelData?.isLiked ?? false
                      ? Assets.images.icons.homeFeed.icLikeFill.path
                      : Assets.images.icons.homeFeed.icUnlike.path,
                  iconColor: reelData?.isLiked ?? false ? null : Colors.white,
                  onTap: () {
                    reelData?.toggleLike();
                    context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: reelData?.id ?? 0));
                    context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
                    Logger.lOG("like${reelData?.likes}");
                  },
                ),
                buildSizedBoxH(16),
                _buildActionButton(
                  context,
                  iconPath: Assets.images.icons.homeFeed.icComment.path,
                  iconColor: Colors.white,
                  onTap: () {
                    showModalBottomSheet(
                      context: context,
                      useRootNavigator: true,
                      isScrollControlled: true,
                      builder: (context) => CommentsBottomSheet(postId: reelData?.id ?? 0),
                    );
                  },
                ),
                buildSizedBoxH(16),
                _buildActionButton(
                  context,
                  onTap: () {
                    context.read<HomeFeedBloc>().add(UserSearchQueryChanged(''));
                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      useRootNavigator: true,
                      backgroundColor: Colors.transparent,
                      builder: (context) => ShareBottomSheet(postId: reelData?.id.toString() ?? '', shareType: 'fxs'),
                    );
                  },
                  iconPath: Assets.images.icons.homeFeed.icShare.path,
                  iconColor: Colors.white,
                ),
                buildSizedBoxH(16),
                _buildActionButton(
                  context,
                  iconPath: reelData?.isSaved ?? false
                      ? Assets.images.pngs.homeFeed.svgSaveFill.path
                      : Assets.images.pngs.homeFeed.svgSave.path,
                  iconColor: reelData?.isSaved ?? false ? null : Colors.white,
                  onTap: isPostPermissionNotifier.value ?? false
                      ? () {
                          reelData?.toggleSave();
                          VibrationHelper.singleShortBuzz();
                          context.read<HomeFeedBloc>().add(SavedPostSocketEvent(postId: reelData?.id.toString() ?? ''));
                        }
                      : () {
                          showToastNoPermission(access: "save post");
                        },
                ),
                buildSizedBoxH(16),
                InkWell(
                    onTap: () {
                      final isMyReel = widget.reelData?.user.userId.toString() == currentUserId;
                      final hasPostPermission = isPostPermissionNotifier.value ?? false;
                      if ((isMyReel && hasPostPermission) || (!isMyReel && isBlockPermission)) {
                        showModalBottomSheet(
                          useRootNavigator: true,
                          context: context,
                          backgroundColor: Colors.transparent,
                          builder: (context) {
                            return Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.vertical(top: Radius.circular(40.0.r)),
                              ),
                              padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 12.w),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    height: 3.h,
                                    width: 40.w,
                                    decoration: BoxDecoration(
                                      color: Color(0xffE9EBEA),
                                      borderRadius: BorderRadius.circular(100.r),
                                    ),
                                  ),
                                  buildSizedBoxH(8.h),
                                  widget.reelData?.user.userId.toString() != currentUserId
                                      ? BlocBuilder<HomeFeedBloc, HomeFeedState>(
                                          builder: (context, state) {
                                            return _buildMoreBottomsheetItem(
                                                imagePath: Assets.images.svg.setting.svgBlockedPeople.path,
                                                label: Lang.of(context).lbl_block,
                                                onTap: () {
                                                  NavigatorService.goBack();
                                                  showDialog(
                                                    context: context,
                                                    builder: (ctx) {
                                                      bool isLoading = false;
                                                      return WillPopScope(
                                                        onWillPop: () async => false,
                                                        child: StatefulBuilder(
                                                          builder: (ctx, setState) {
                                                            return CustomAlertDialog(
                                                              imagePath: Assets.images.pngs.other.pngBlock.path,
                                                              imageheight: 30.h,
                                                              imagewidth: 30.w,
                                                              title: "Block User?",
                                                              subtitle: Lang.of(ctx).msg_block_user_title,
                                                              onConfirmButtonPressed: () async {
                                                                setState(() {
                                                                  isLoading = true;
                                                                });

                                                                final userId =
                                                                    widget.reelData?.user.userId.toString() ?? '';

                                                                ctx.read<HomeFeedBloc>().add(BlockUserApiEvent(
                                                                    userId: userId, context: ctx, isblocked: false));

                                                                Future.delayed(Duration(seconds: 1), () {
                                                                  // Remove reels from the current service instance
                                                                  if (userId.isNotEmpty) {
                                                                    final removedCount = widget.reelService
                                                                            ?.removeReelsByUserId(userId) ??
                                                                        0;
                                                                    Logger.lOG(
                                                                        'Blocked user $userId: $removedCount reels removed');
                                                                  }
                                                                });

                                                                VibrationHelper.singleShortBuzz();
                                                              },
                                                              confirmButtonText: Lang.of(ctx).lbl_confirm,
                                                              isLoading: isLoading,
                                                            );
                                                          },
                                                        ),
                                                      );
                                                    },
                                                  );
                                                },
                                                isPermission: isBlockPermission,
                                                massageLabel: 'Block Post');
                                          },
                                        )
                                      : Column(
                                          children: [
                                            if (isPostPermissionNotifier.value ?? false)
                                              BlocBuilder<HomeFeedBloc, HomeFeedState>(
                                                builder: (context, state) {
                                                  PostData? matchedPost;
                                                  VideoData? matchedVideo;

                                                  try {
                                                    matchedPost = state.getProfilePost.firstWhere(
                                                      (p) => p.id.toString() == widget.reelData?.id.toString(),
                                                    );
                                                  } catch (e) {
                                                    Logger.lOG("Error $e");
                                                  }

                                                  try {
                                                    matchedVideo = state.getProfilevideo.firstWhere(
                                                      (v) => v.id.toString() == widget.reelData?.id.toString(),
                                                    );
                                                  } catch (e) {
                                                    Logger.lOG("Error $e");
                                                  }

                                                  return _buildMoreBottomsheetItem(
                                                      imagePath: Assets.images.svg.homeFeed.svgEdit.path,
                                                      label: Lang.of(context).lbl_edit,
                                                      onTap: () async {
                                                        try {
                                                          NavigatorService.goBack();
                                                          NavigatorService.pushNamed(
                                                            AppRoutes.EditPostscreen,
                                                            arguments: [
                                                              matchedPost,
                                                              matchedVideo,
                                                              0,
                                                              true,
                                                              false,
                                                            ],
                                                          );
                                                        } catch (e) {
                                                          Logger.lOG("Edit error $e");
                                                        }
                                                      },
                                                      isPermission: isPostPermission,
                                                      massageLabel: 'Edit Post');
                                                },
                                              ),
                                            if (isPostPermissionNotifier.value ?? false)
                                              _buildMoreBottomsheetItem(
                                                  imagePath: Assets.images.svg.setting.svgDeleteAccount.path,
                                                  label: Lang.of(context).lbl_delete,
                                                  onTap: () {
                                                    NavigatorService.goBack();
                                                    showDialog(
                                                      context: context,
                                                      builder: (ctx) {
                                                        bool isLoading = false;

                                                        return WillPopScope(
                                                          onWillPop: () async => false,
                                                          child: StatefulBuilder(
                                                            builder: (ctx, setState) {
                                                              return BlocBuilder<HomeFeedBloc, HomeFeedState>(
                                                                builder: (context, state) {
                                                                  final index = state.reels.indexWhere(
                                                                      (reel) => reel.id == widget.reelData?.id);
                                                                  return CustomAlertDialog(
                                                                    imageheight: 70.h,
                                                                    imagewidth: 70.w,
                                                                    imagePath:
                                                                        Assets.images.pngs.other.pngDeletePost.path,
                                                                    title: Lang.of(context).lbl_delete_post_title,
                                                                    subtitle: '',
                                                                    onConfirmButtonPressed: () async {
                                                                      setState(() {
                                                                        isLoading = true;
                                                                      });

                                                                      final reelId = reelData?.id ?? 0;

                                                                      context.read<HomeFeedBloc>().add(
                                                                          DeletePostApiEvent(
                                                                              postId: reelId,
                                                                              index: index,
                                                                              isNavigate: false,
                                                                              isNotificationPost: false));

                                                                      Future.delayed(Duration(seconds: 1), () {
                                                                        // Remove reel from the current service instance
                                                                        if (reelId > 0) {
                                                                          final wasRemoved = widget.reelService
                                                                                  ?.removeReelById(reelId) ??
                                                                              false;
                                                                          Logger.lOG(
                                                                              'Deleted reel $reelId: ${wasRemoved ? "removed" : "not found"}');
                                                                        }
                                                                      });

                                                                      VibrationHelper.singleShortBuzz();
                                                                    },
                                                                    confirmButtonText: Lang.of(ctx).lbl_delete,
                                                                    isLoading: isLoading,
                                                                  );
                                                                },
                                                              );
                                                            },
                                                          ),
                                                        );
                                                      },
                                                    );
                                                  },
                                                  isPermission: isPostPermission,
                                                  massageLabel: 'Delete Post'),
                                          ],
                                        ),
                                ],
                              ),
                            );
                          },
                          isScrollControlled: true,
                        );
                      } else {
                        showToastNoPermission(access: "");
                      }
                    },
                    child: Icon(
                      Icons.more_vert_sharp,
                      color: Theme.of(context).customColors.white,
                      size: 26.sp,
                    )),
                buildSizedBoxH(75),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMoreBottomsheetItem({
    required String imagePath,
    required String label,
    required VoidCallback onTap,
    required bool isPermission,
    required String massageLabel,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 6.h),
      decoration: BoxDecoration(
        color: isPermission
            ? Theme.of(context).primaryColor.withOpacity(0.2)
            : Theme.of(context).primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(30.r),
      ),
      child: ListTile(
        minTileHeight: 60.h,
        leading: CustomImageView(
          height: 20.h,
          width: 20.w,
          imagePath: imagePath,
          fit: BoxFit.contain,
          color: !isPermission ? Colors.black45 : null,
        ),
        title: Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontSize: 16.sp,
                color: !isPermission ? Colors.black45 : null,
              ),
        ),
        onTap: isPermission
            ? onTap
            : () {
                NavigatorService.goBack();
                showToastNoPermission(access: massageLabel);
              },
      ),
    );
  }

  Widget _buildActionButton(BuildContext context,
      {String? iconPath, Color? iconColor, void Function()? onTap, int? count}) {
    return InkWell(
      onTap: onTap,
      child: Column(
        children: [
          CustomImageView(
            height: 26.h,
            width: 26.w,
            fit: BoxFit.contain,
            imagePath: iconPath,
            color: iconColor,
          ),
          if (count != null) buildSizedBoxH(4),
          if (count != null)
            Text(
              abbreviateNumber(count),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Theme.of(context).customColors.white),
            ),
        ],
      ),
    );
  }
}
