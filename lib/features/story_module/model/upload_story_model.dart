import 'package:json_annotation/json_annotation.dart';

part 'upload_story_model.g.dart';

UploadStoryModel deserializeUploadStoryModel(Map<String, dynamic> json) => UploadStoryModel.fromJson(json);

@JsonSerializable()
class UploadStoryModel {
  final bool? status;
  final String? message;

  UploadStoryModel({this.status, this.message});

  factory UploadStoryModel.fromJson(Map<String, dynamic> json) => _$UploadStoryModelFromJson(json);

  Map<String, dynamic> toJson() => _$UploadStoryModelToJson(this);
}
