import 'package:flowkar/features/story_module/model/get_single_story_model.dart';

GetAllStoryModel deserializeGetAllStoryModel(Map<String, dynamic> json) => GetAllStoryModel.fromJson(json);

class GetAllStoryModel {
  int? count;
  String? next;
  String? previous;
  Results? results;

  GetAllStoryModel({this.count, this.next, this.previous, this.results});

  GetAllStoryModel.fromJson(Map<String, dynamic> json) {
    count = json['count'];
    next = json['next'];
    previous = json['previous'];
    results = json['results'] != null ? Results.fromJson(json['results']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['count'] = count;
    data['next'] = next;
    data['previous'] = previous;
    if (results != null) {
      data['results'] = results!.toJson();
    }
    return data;
  }
}

class Results {
  bool? status;
  String? message;
  List<StoryData>? data;

  Results({this.status, this.message, this.data});

  Results.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      data = <StoryData>[];
      json['data'].forEach((v) {
        data!.add(StoryData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class StoryData {
  int? id;
  int? userId;
  List<String>? storyData;
  String? username;
  String? profileImage;
  String? music;
  bool? isDeleted;
  bool? isArchieved;
  bool? isHighlighted;
  String? createdOn;
  List<Likes>? likes;
  List<Views>? views;
  int? totalViews;
  int? previousUserId;
  int? nextUserId;

  StoryData(
      {this.id,
      this.userId,
      this.storyData,
      this.username,
      this.profileImage,
      this.music,
      this.isDeleted,
      this.isArchieved,
      this.isHighlighted,
      this.createdOn,
      this.likes,
      this.views,
      this.totalViews,
      this.previousUserId,
      this.nextUserId});

  StoryData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    storyData = json['story_data'].cast<String>();
    username = json['username'];
    profileImage = json['profile_image'];
    music = json['music'];
    isDeleted = json['is_deleted'];
    isArchieved = json['is_archieved'];
    isHighlighted = json['is_highlighted'];
    createdOn = json['created_on'];
    if (json['likes'] != null) {
      likes = <Likes>[];
      json['likes'].forEach((v) {
        likes!.add(Likes.fromJson(v));
      });
    }
    if (json['views'] != null) {
      views = <Views>[];
      json['views'].forEach((v) {
        views!.add(Views.fromJson(v));
      });
    }
    totalViews = json['total_views'];
    previousUserId = json['previous_user_id'];
    nextUserId = json['next_user_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['story_data'] = storyData;
    data['username'] = username;
    data['profile_image'] = profileImage;
    data['music'] = music;
    data['is_deleted'] = isDeleted;
    data['is_archieved'] = isArchieved;
    data['is_highlighted'] = isHighlighted;
    data['created_on'] = createdOn;
    if (likes != null) {
      data['likes'] = likes!.map((v) => v.toJson()).toList();
    }
    if (views != null) {
      data['views'] = views!.map((v) => v.toJson()).toList();
    }
    data['total_views'] = totalViews;
    data['previous_user_id'] = previousUserId;
    data['next_user_id'] = nextUserId;
    return data;
  }
}

class Likes {
  String? username;
  String? profileImage;

  Likes({this.username, this.profileImage});

  Likes.fromJson(Map<String, dynamic> json) {
    username = json['username'];
    profileImage = json['profile_image'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['username'] = username;
    data['profile_image'] = profileImage;
    return data;
  }
}
