import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/story_feature_demo/Story_view_demo/story_view/models/story_model.dart';
import 'package:flowkar/features/story_module/New_story_code/new_story_code.dart';
import 'package:flowkar/features/story_module/New_story_code/src/controller/flutter_story_controller.dart';
import 'package:flowkar/features/story_module/New_story_code/src/models/story_view_indicator_config.dart';
import 'package:flowkar/features/story_module/New_story_code/src/story_presenter/story_view.dart';

class MyStoryView extends StatefulWidget {
  const MyStoryView(
      {super.key,
      required this.storyModel,
      required this.pageController,
      required this.storydata,
      this.stdata,
      this.dataIndex});

  final StoryModel storyModel;
  final PageController pageController;
  final int? dataIndex;
  final NewStory storydata;
  final List<dynamic>? stdata;

  @override
  State<MyStoryView> createState() => _MyStoryViewState();
}

class _MyStoryViewState extends State<MyStoryView> {
  late FlutterStoryController controller;
  int storyIndex = 0;

  @override
  void initState() {
    controller = FlutterStoryController();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final storyViewIndicatorConfig = StoryViewIndicatorConfig(
      height: 4,
      activeColor: Colors.white,
      backgroundCompletedColor: Colors.white,
      // ignore: deprecated_member_use
      backgroundDisabledColor: Colors.white.withOpacity(0.5),
      horizontalGap: 1,
      borderRadius: 1.5,
    );

    storyIndex = widget.dataIndex!;

    return FlutterStoryView(
      onSlideDown: (p0) {
        NavigatorService.goBack();
      },
      pageController: widget.pageController,
      flutterStoryController: controller,
      items: widget.storyModel.stories,
      stdata: widget.stdata,
      storyData: widget.storyModel,
      dataIndex: widget.dataIndex ?? 0,
      storyViewIndicatorConfig: storyViewIndicatorConfig,
      initialIndex: 0,
      // headerWidget: ProfileView(
      //   storyModel: widget.storyModel,
      //   flutterStoryController: controller,
      // ),
      onStoryChanged: (p0) {},
      onPreviousCompleted: () async {
        await widget.pageController.previousPage(duration: const Duration(milliseconds: 500), curve: Curves.decelerate);
      },
      onCompleted: () async {
        await widget.pageController.nextPage(duration: const Duration(milliseconds: 500), curve: Curves.decelerate);
        controller = FlutterStoryController();
      },
    );
  }
}
