import 'package:cached_network_image/cached_network_image.dart';
import 'package:flowkar/features/story_module/New_story_code/src/models/story_view_audio_config.dart';
import 'package:flutter/material.dart';

class StoryViewImageConfig {
  const StoryViewImageConfig({
    this.fit,
    this.height,
    this.width,
    this.progressIndicatorBuilder,
    this.audioConfig,
  });

  /// Height for the ImageBuilder
  final double? height;

  /// Width for the ImageBuilder
  final double? width;

  /// BoxFit settings for the ImageBuilder
  final BoxFit? fit;

  final StoryViewAudioConfig? audioConfig;

  /// Progress Indicator for building image
  final Widget Function(BuildContext, String, DownloadProgress)? progressIndicatorBuilder;
}
