import 'package:flowkar/features/story_feature_demo/Story_view_demo/story_view/models/story_model.dart';
// import 'package:flowkar/features/story_module/New_story_code/new_story_model.dart';
import 'package:flowkar/features/story_module/New_story_code/src/controller/flutter_story_controller.dart';
import 'package:flowkar/features/story_module/New_story_code/src/models/story_view_audio_config.dart';
import 'package:flowkar/features/story_module/New_story_code/src/models/story_view_image_config.dart';
import 'package:flowkar/features/story_module/New_story_code/src/models/story_view_text_config.dart';
import 'package:flowkar/features/story_module/New_story_code/src/models/story_view_video_config.dart';
import 'package:flowkar/features/story_module/New_story_code/src/models/story_view_web_config.dart';
import 'package:flowkar/features/story_module/New_story_code/src/utils/story_utils.dart';
import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';

class StoryItem {
  StoryItem(
      {this.url,
      required this.storyItemType,
      required this.storyID,
      this.thumbnail,
      this.isMuteByDefault = false,
      this.duration = const Duration(seconds: 3),
      this.storyItemSource = StoryItemSource.network,
      this.videoConfig,
      this.errorWidget,
      this.imageConfig,
      this.textConfig,
      this.webConfig,
      this.customWidget,
      this.audioConfig,
      this.stories,
      required this.islike})
      : assert(
          storyItemType == StoryItemType.custom || url != null,
          'URL is required when storyItemType is not custom',
        ),
        assert(
          storyItemType != StoryItemType.custom || customWidget != null,
          'CustomWidget is required when storyItemType is custom',
        );

  /// Duration of displaying the widget
  final Duration duration;

  /// Widget to display beneath main view as thumbnail
  final Widget? thumbnail;

  /// Widget to display when error occurs loading View
  final Widget? errorWidget;

  /// Custom Widget to display fully instead of any other view
  final Widget? Function(FlutterStoryController?, AudioPlayer? audioPlayer)? customWidget;

  final StoryItemType storyItemType;
  final int storyID;
  bool islike;

  /// Asset URL, File Path or Web URL
  final String? url;

  /// Applicable when [storyItemType] is [StoryItemType.video]
  final bool isMuteByDefault;

  /// Defaults to [StoryItemSource.network]
  final StoryItemSource storyItemSource;

  /// Applicable when [storyItemType] is [StoryItemType.image]
  final StoryViewImageConfig? imageConfig;

  /// Applicable when [storyItemType] is [StoryItemType.video]
  final StoryViewVideoConfig? videoConfig;

  /// Applicable when you want to add audio in [StoryItemType.image],[StoryItemType.text],[StoryItemType.custom]
  final StoryViewAudioConfig? audioConfig;

  /// Applicable when [storyItemType] is [StoryItemType.text]
  final StoryViewTextConfig? textConfig;

  /// Applicable when [storyItemType] is [StoryItemType.web]
  final StoryViewWebConfig? webConfig;
  final List<Stories>? stories;
}
