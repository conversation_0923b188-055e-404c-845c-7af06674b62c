import 'dart:async';
import 'dart:developer';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/story_module/New_story_code/new_story_code.dart';
import 'package:flowkar/features/story_module/New_story_code/src/story_presenter/story_custom_view_wrapper.dart';
import 'package:flowkar/features/widgets/common/get_user_profile_by_id/get_user_profile_by_id.dart';
import 'package:flutter_speed_dial/flutter_speed_dial.dart';
import 'package:just_audio/just_audio.dart';
import 'package:video_player/video_player.dart';
import 'story_view_indicator.dart';
import '../models/story_item.dart';
import '../models/story_view_indicator_config.dart';
import '../controller/flutter_story_controller.dart';
import 'image_story_view.dart';
import 'video_story_view.dart';
import 'web_story_view.dart';
import 'text_story_view.dart';
import '../utils/smooth_video_progress.dart';
import '../utils/story_utils.dart';

typedef OnStoryChanged = void Function(int);
typedef OnCompleted = Future<void> Function();
typedef OnLeftTap = void Function();
typedef OnRightTap = void Function();
typedef OnDrag = void Function();
typedef OnItemBuild = Widget? Function(int, Widget);
typedef OnVideoLoad = void Function(VideoPlayerController?);
typedef OnAudioLoaded = void Function(AudioPlayer);
typedef CustomViewBuilder = Widget Function(AudioPlayer);
typedef OnSlideDown = void Function(DragUpdateDetails);
typedef OnSlideStart = void Function(DragStartDetails);

class FlutterStoryView extends StatefulWidget {
  const FlutterStoryView({
    this.flutterStoryController,
    this.items = const [],
    this.onStoryChanged,
    this.onLeftTap,
    this.onRightTap,
    this.onCompleted,
    this.onPreviousCompleted,
    this.initialIndex = 0,
    this.storyViewIndicatorConfig,
    this.restartOnCompleted = true,
    this.onVideoLoad,
    this.headerWidget,
    this.onCallback,
    // this.footerWidget,
    this.onSlideDown,
    this.onSlideStart,
    this.stdata,
    required this.storyData,
    required this.pageController,
    required this.dataIndex,
    super.key,
  }) : assert(initialIndex < items.length);

  /// List of StoryItem objects to display in the story view.
  final List<StoryItem> items;
  final List<dynamic>? stdata;
  final StoryModel storyData;
  final int dataIndex;

  final PageController pageController;

  /// Controller for managing the current playing media.
  final FlutterStoryController? flutterStoryController;

  /// Callback function triggered whenever the story changes or the user navigates to the previous/next story.
  final OnStoryChanged? onStoryChanged;

  /// Callback function triggered when all items in the list have been played.
  final OnCompleted? onCompleted;

  /// Callback function triggered when all items in the list have been played.
  final OnCompleted? onPreviousCompleted;

  /// Callback function triggered when the user taps on the left half of the screen.
  final OnLeftTap? onLeftTap;

  /// Callback function triggered when the user taps on the right half of the screen.
  final OnRightTap? onRightTap;

  /// Callback function triggered when user drag downs the storyview.
  final OnSlideDown? onSlideDown;

  /// Callback function triggered when user starts drag downs the storyview.
  final OnSlideStart? onSlideStart;

  /// Indicates whether the story view should restart from the beginning after all items have been played.
  final bool restartOnCompleted;

  /// Index to start playing the story from initially.
  final int initialIndex;

  /// Configuration and styling options for the story view indicator.
  final StoryViewIndicatorConfig? storyViewIndicatorConfig;

  /// Callback function to retrieve the VideoPlayerController when it is initialized and ready to play.
  final OnVideoLoad? onVideoLoad;

  /// Widget to display user profile or other details at the top of the screen.
  final Widget? headerWidget;

  final Future<void> Function()? onCallback;

  /// Widget to display text field or other content at the bottom of the screen.
  // final Widget? footerWidget;

  @override
  State<FlutterStoryView> createState() => _FlutterStoryViewState();
}

class _FlutterStoryViewState extends State<FlutterStoryView>
    with WidgetsBindingObserver, SingleTickerProviderStateMixin {
  AnimationController? _animationController;
  Animation? _currentProgressAnimation;
  int currentIndex = 0;
  bool isCurrentItemLoaded = false;
  double currentItemProgress = 0;
  VideoPlayerController? _currentVideoPlayer;
  double? storyViewHeight;
  AudioPlayer? _audioPlayer;
  Duration? _totalAudioDuration;
  StreamSubscription? _audioDurationSubscriptionStream;
  StreamSubscription? _audioPlayerStateStream;
  final ValueNotifier<bool> isLikedNotifier = ValueNotifier<bool>(false);
  final loggedInUserId = Prefobj.preferences?.get(Prefkeys.USER_ID);

  bool islike = false;
  bool isOpenDialog = false;

  @override
  void initState() {
    if (_animationController != null) {
      _animationController?.reset();
      _animationController?.dispose();
      _animationController = null;
    }
    _animationController = AnimationController(
      vsync: this,
    );
    currentIndex = widget.initialIndex;
    widget.flutterStoryController?.addListener(_storyControllerListener);
    _startStoryView();

    WidgetsBinding.instance.addObserver(this);

    // Logger.lOG(
    //     "_))(__(*(*)_)(_)(__)(_(_)(_____)))______________________${widget.stdata?.stories?.length}");

    super.initState();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    log("STATE ==> $state");
    switch (state) {
      case AppLifecycleState.resumed:
        _resumeMedia();
        break;
      case AppLifecycleState.inactive:
      case AppLifecycleState.paused:
      case AppLifecycleState.hidden:
        _pauseMedia();
        break;
      case AppLifecycleState.detached:
        break;
    }
  }

  @override
  void dispose() {
    _animationController?.dispose();
    _animationController = null;
    // widget.flutterStoryController
    //   ?..removeListener(_storyControllerListener)
    //   ..dispose();
    _audioDurationSubscriptionStream?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  /// Returns the current story item.
  StoryItem get currentItem => widget.items[currentIndex];

  /// Returns the configuration for the story view indicator.
  StoryViewIndicatorConfig get storyViewIndicatorConfig =>
      widget.storyViewIndicatorConfig ?? const StoryViewIndicatorConfig();

  /// Listener for the story controller to handle various story actions.
  void _storyControllerListener() {
    final controller = widget.flutterStoryController;
    final storyStatus = controller?.storyStatus;
    final jumpIndex = controller?.jumpIndex;

    if (storyStatus != null) {
      if (storyStatus.isPlay) {
        _resumeMedia();
      } else if (storyStatus.isMute || storyStatus.isUnMute) {
        _toggleMuteUnMuteMedia();
      } else if (storyStatus.isPause) {
        _pauseMedia();
      } else if (storyStatus.isPrevious) {
        _playPrevious();
      } else if (storyStatus.isNext) {
        _playNext();
      }
    }

    if (jumpIndex != null && jumpIndex >= 0 && jumpIndex < widget.items.length) {
      currentIndex = jumpIndex - 1;
      _playNext();
    }
  }

  /// Starts the story view.
  void _startStoryView() {
    widget.onStoryChanged?.call(currentIndex);
    _playMedia();
    if (mounted) {
      setState(() {});
    }
  }

  /// Resets the animation controller and its listeners.
  void _resetAnimation() {
    _animationController?.reset();
    _animationController
      ?..removeListener(animationListener)
      ..removeStatusListener(animationStatusListener);
  }

  /// Initializes and starts the media playback for the current story item.
  void _playMedia() {
    isCurrentItemLoaded = false;
  }

  /// Resumes the media playback.
  void _resumeMedia() {
    _audioPlayer?.play();
    _currentVideoPlayer?.play();
    if (_currentProgressAnimation != null) {
      _animationController?.forward(
        from: _currentProgressAnimation?.value,
      );
    }
  }

  /// Starts the countdown for the story item duration.
  void _startStoryCountdown() {
    _currentVideoPlayer?.addListener(videoListener);
    if (_currentVideoPlayer != null) {
      return;
    }

    if (currentItem.audioConfig != null) {
      _audioPlayer?.durationFuture?.then((v) {
        _totalAudioDuration = v;
        _animationController ??= AnimationController(
          vsync: this,
        );

        _animationController?.duration = v;

        _currentProgressAnimation = Tween<double>(begin: 0, end: 1).animate(_animationController!)
          ..addListener(animationListener)
          ..addStatusListener(animationStatusListener);

        _animationController!.forward();
      });
      _audioDurationSubscriptionStream = _audioPlayer?.positionStream.listen(audioPositionListener);
      _audioPlayerStateStream = _audioPlayer?.playerStateStream.listen(
        (event) {
          if (event.playing) {
            if (event.processingState == ProcessingState.buffering) {
              _pauseMedia();
            } else if (event.processingState == ProcessingState.loading) {
              _pauseMedia();
            } else {
              _resumeMedia();
            }
          }
        },
      );
      return;
    }

    _animationController ??= AnimationController(
      vsync: this,
    );

    _animationController?.duration = _currentVideoPlayer?.value.duration ?? currentItem.duration;

    _currentProgressAnimation = Tween<double>(begin: 0, end: 1).animate(_animationController!)
      ..addListener(animationListener)
      ..addStatusListener(animationStatusListener);

    _animationController!.forward();
  }

  /// Listener for the video player's state changes.
  void videoListener() {
    final dur = _currentVideoPlayer?.value.duration.inMilliseconds;
    final pos = _currentVideoPlayer?.value.position.inMilliseconds;

    if (pos == dur) {
      _playNext();
      return;
    }

    if (_currentVideoPlayer?.value.isBuffering ?? false) {
      _animationController?.stop(canceled: false);
    }

    if (_currentVideoPlayer?.value.isPlaying ?? false) {
      if (_currentProgressAnimation != null) {
        _animationController?.forward(from: _currentProgressAnimation?.value);
      }
    }
  }

  void audioPositionListener(Duration position) {
    final dur = position.inMilliseconds;
    final pos = _totalAudioDuration?.inMilliseconds;

    if (pos == dur) {
      _playNext();
      return;
    }
  }

  /// Listener for the animation progress.
  void animationListener() {
    currentItemProgress = _animationController?.value ?? 0;
  }

  /// Listener for the animation status.
  void animationStatusListener(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      _playNext();
    }
  }

  /// Pauses the media playback.
  void _pauseMedia() {
    _audioPlayer?.pause();
    _currentVideoPlayer?.pause();
    _animationController?.stop(canceled: false);
  }

  /// Toggles mute/unmute for the media.
  void _toggleMuteUnMuteMedia() {
    if (_currentVideoPlayer != null) {
      final videoPlayerValue = _currentVideoPlayer!.value;
      if (videoPlayerValue.volume == 1) {
        _currentVideoPlayer!.setVolume(0);
      } else {
        _currentVideoPlayer!.setVolume(1);
      }
    }
  }

  /// Plays the next story item.
  void _playNext() async {
    if (widget.items.length == 1 && _currentVideoPlayer != null && widget.restartOnCompleted) {
      await widget.onCompleted?.call();

      /// In case of story length 1 with video, we won't initialise,
      /// instead we will loop the video
      return;
    }
    if (_currentVideoPlayer != null && currentIndex != (widget.items.length - 1)) {
      /// Dispose the video player only in case of multiple story
      _currentVideoPlayer?.removeListener(videoListener);
      _currentVideoPlayer?.dispose();
      _currentVideoPlayer = null;
    }

    if (currentIndex == widget.items.length - 1) {
      await widget.onCompleted?.call();
      if (widget.restartOnCompleted) {
        currentIndex = 0;
        _resetAnimation();
        _startStoryView();
      }
      if (mounted) {
        setState(() {});
      }
      return;
    }

    currentIndex = currentIndex + 1;
    _resetAnimation();
    widget.onStoryChanged?.call(currentIndex);
    _playMedia();
    if (mounted) {
      setState(() {});
    }
  }

  /// Plays the previous story item.
  void _playPrevious() {
    if (_audioPlayer != null) {
      _audioPlayer?.dispose();
      _audioDurationSubscriptionStream?.cancel();
      _audioPlayerStateStream?.cancel();
    }
    if (_currentVideoPlayer != null) {
      _currentVideoPlayer?.removeListener(videoListener);
      _currentVideoPlayer?.dispose();
      _currentVideoPlayer = null;
    }

    if (currentIndex == 0) {
      _resetAnimation();
      _startStoryCountdown();
      if (mounted) {
        setState(() {});
      }
      widget.onPreviousCompleted?.call();
      return;
    }

    _resetAnimation();
    currentIndex = currentIndex - 1;
    widget.onStoryChanged?.call(currentIndex);
    _playMedia();
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Padding(
      padding: EdgeInsets.only(
        top: 30,
        bottom: MediaQuery.of(context).viewPadding.bottom,
      ),
      child: Stack(
        children: [
          if (currentItem.thumbnail != null) ...{
            currentItem.thumbnail!,
          },
          if (currentItem.storyItemType.isCustom && currentItem.customWidget != null) ...{
            Positioned.fill(
              key: UniqueKey(),
              child: StoryCustomWidgetWrapper(
                builder: (audioPlayer) {
                  return currentItem.customWidget!(widget.flutterStoryController, audioPlayer) ??
                      const SizedBox.shrink();
                },
                storyItem: currentItem,
                onLoaded: () {
                  isCurrentItemLoaded = true;
                  _startStoryCountdown();
                },
                onAudioLoaded: (audioPlayer) {
                  isCurrentItemLoaded = true;
                  _audioPlayer = audioPlayer;
                  _startStoryCountdown();
                },
              ),
            ),
          },
          if (currentItem.storyItemType.isImage) ...{
            Positioned.fill(
              key: UniqueKey(),
              child: ImageStoryView(
                key: ValueKey('$currentIndex'),
                storyItem: currentItem,
                onImageLoaded: (isLoaded) {
                  isCurrentItemLoaded = isLoaded;
                  _startStoryCountdown();
                },
                onAudioLoaded: (audioPlayer) {
                  isCurrentItemLoaded = true;
                  _audioPlayer = audioPlayer;
                  _startStoryCountdown();
                },
              ),
            ),
          },
          if (currentItem.storyItemType.isVideo) ...{
            Positioned.fill(
              child: VideoStoryView(
                storyItem: currentItem,
                key: ValueKey('$currentIndex'),
                looping: widget.items.length == 1 && widget.restartOnCompleted,
                onVideoLoad: (videoPlayer) {
                  isCurrentItemLoaded = true;
                  _currentVideoPlayer = videoPlayer;
                  widget.onVideoLoad?.call(videoPlayer);
                  _startStoryCountdown();
                  if (mounted) {
                    setState(() {});
                  }
                },
              ),
            ),
          },
          if (currentItem.storyItemType.isWeb) ...{
            Positioned.fill(
              child: WebStoryView(
                storyItem: currentItem,
                key: ValueKey('$currentIndex'),
                onWebViewLoaded: (controller, loaded) {
                  isCurrentItemLoaded = loaded;
                  if (loaded) {
                    _startStoryCountdown();
                  }
                  currentItem.webConfig?.onWebViewLoaded?.call(controller, loaded);
                },
              ),
            ),
          },
          if (currentItem.storyItemType.isText) ...{
            Positioned.fill(
              key: UniqueKey(),
              child: TextStoryView(
                storyItem: currentItem,
                key: ValueKey('$currentIndex'),
                onTextStoryLoaded: (loaded) {
                  isCurrentItemLoaded = loaded;
                  _startStoryCountdown();
                },
                onAudioLoaded: (audioPlayer) {
                  isCurrentItemLoaded = true;
                  _audioPlayer = audioPlayer;
                  _startStoryCountdown();
                },
              ),
            ),
          },
          Align(
            alignment: storyViewIndicatorConfig.alignment,
            child: Padding(
              padding: storyViewIndicatorConfig.margin,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _currentVideoPlayer != null
                      ? SmoothVideoProgress(
                          controller: _currentVideoPlayer!,
                          builder: (context, progress, duration, child) {
                            return StoryViewIndicator(
                              currentIndex: currentIndex,
                              currentItemAnimatedValue: progress.inMilliseconds / duration.inMilliseconds,
                              totalItems: widget.items.length,
                              storyViewIndicatorConfig: storyViewIndicatorConfig,
                            );
                          })
                      : _animationController != null
                          ? AnimatedBuilder(
                              animation: _animationController!,
                              builder: (context, child) => StoryViewIndicator(
                                currentIndex: currentIndex,
                                currentItemAnimatedValue: currentItemProgress,
                                totalItems: widget.items.length,
                                storyViewIndicatorConfig: storyViewIndicatorConfig,
                              ),
                            )
                          : StoryViewIndicator(
                              currentIndex: currentIndex,
                              currentItemAnimatedValue: currentItemProgress,
                              totalItems: widget.items.length,
                              storyViewIndicatorConfig: storyViewIndicatorConfig,
                            ),
                ],
              ),
            ),
          ),
          Align(
            alignment: Alignment.centerLeft,
            child: SizedBox(
              width: size.width * .2,
              height: size.height,
              child: GestureDetector(
                onTap: _playPrevious,
              ),
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: SizedBox(
              width: size.width * .2,
              height: size.height,
              child: GestureDetector(
                onTap: _playNext,
              ),
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: SizedBox(
              width: size.width,
              height: size.height,
              child: GestureDetector(
                key: ValueKey('$currentIndex'),
                onLongPressDown: (details) => _pauseMedia(),
                onLongPressUp: _resumeMedia,
                onLongPressEnd: (details) => _resumeMedia(),
                onLongPressCancel: _resumeMedia,
                onVerticalDragStart: widget.onSlideStart?.call,
                onVerticalDragUpdate: widget.onSlideDown?.call,
              ),
            ),
          ),

          Align(
            alignment: Alignment.topCenter,
            child: SafeArea(
                bottom: storyViewIndicatorConfig.enableBottomSafeArea,
                top: storyViewIndicatorConfig.enableTopSafeArea,
                // child: widget.headerWidget!),
                child: _buildProfile()),
          ),

          // if (widget.footerWidget != null) ...{

          // Coment TextField or Like Button
          widget.storyData.uid == int.parse(loggedInUserId)
              ? const SizedBox.shrink()
              : Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20).copyWith(bottom: 10),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Spacer(),
                        // Expanded(
                        // child:
                        // Container(
                        //   decoration: BoxDecoration(
                        //     border: Border.all(color: Colors.white),
                        //     borderRadius: BorderRadius.circular(999),
                        //   ),
                        //   padding: const EdgeInsets.symmetric(horizontal: 15),
                        //   child: TextFormField(
                        //     onTap: () {
                        //       // widget.controller.pause();
                        //       widget.flutterStoryController?.pause();
                        //     },
                        //     onTapOutside: (event) {
                        //       // widget.controller.play();
                        //       widget.flutterStoryController?.play();
                        //       FocusScope.of(context).unfocus();
                        //     },
                        //     style: const TextStyle(
                        //       fontWeight: FontWeight.w400,
                        //       color: Colors.white,
                        //     ),
                        //     decoration: const InputDecoration(
                        //       hintStyle: TextStyle(
                        //         fontWeight: FontWeight.w400,
                        //         color: Colors.white,
                        //       ),
                        //       hintText: 'Enter Message',
                        //       border: InputBorder.none,
                        //       isDense: true,
                        //       contentPadding: EdgeInsets.symmetric(vertical: 6),
                        //     ),
                        //   ),
                        // ),
                        // ),
                        const SizedBox(width: 15),
                        ValueListenableBuilder<bool>(
                            valueListenable: isLikedNotifier,
                            builder: (context, isLiked, child) {
                              isLikedNotifier.value = widget.items[currentIndex].islike;
                              return Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  InkWell(
                                    onTap: () {
                                      widget.items[currentIndex].islike = !(widget.items[currentIndex].islike);

                                      widget.stdata?[widget.dataIndex].stories?[currentIndex].isLiked =
                                          widget.items[currentIndex].islike;

                                      isLikedNotifier.value = widget.items[currentIndex].islike;
                                      context
                                          .read<HomeFeedBloc>()
                                          .add(StoryLikeSocketEvent(storyId: widget.items[currentIndex].storyID));

                                      setState(() {});
                                    },
                                    child: CustomImageView(
                                      // color: !widget.items[currentIndex].islike ? AppColors.whitecolor : AppColors.primaryColor,
                                      imagePath: widget.items[currentIndex].islike
                                          ? Assets.images.icons.homeFeed.icLike.path
                                          : Assets.images.icons.homeFeed.icUnlike.path,
                                      height: 24.h,
                                      width: 24.w,
                                      fit: BoxFit.cover,
                                    ),
                                  )
                                ],
                              );
                            }),
                      ],
                    ),
                  ),
                ),
          // },
        ],
      ),
    );
  }

  Widget _buildProfile() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(1),
      ),
      child: Padding(
        padding: EdgeInsets.only(top: 30.h, left: 15.w, right: 15.w),
        child: Row(
          children: [
            GestureDetector(
              onTap: () {
                PersistentNavBarNavigator.pushNewScreen(
                  context,
                  screen: GetUserProfileById(
                    userId: widget.storyData.uid,
                    stackonScreen: true,
                  ),
                );
              },
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.6),
                  shape: BoxShape.circle,
                ),
                padding: const EdgeInsets.all(1),
                child: ClipOval(
                    child: CustomImageView(
                  height: 40.h,
                  width: 40.w,
                  imagePath: widget.storyData.userProfile == ''
                      ? Assets.images.pngs.other.pngPlaceholder.path
                      : widget.storyData.userProfile,
                )),
              ),
            ),
            buildSizedBoxW(10.0),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  PersistentNavBarNavigator.pushNewScreen(
                    context,
                    screen: GetUserProfileById(
                      userId: widget.storyData.uid,
                      stackonScreen: true,
                    ),
                  );
                },
                child: Row(
                  children: [
                    Flexible(
                      child: Text(
                        widget.storyData.userName,
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium
                            ?.copyWith(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (widget.storyData.uid == int.parse(loggedInUserId))
              SpeedDial(
                backgroundColor: Colors.transparent,
                elevation: 0,
                overlayOpacity: 0.0,
                onOpen: () {
                  widget.flutterStoryController?.pause();
                },
                onClose: () {
                  widget.flutterStoryController?.play();
                },
                direction: SpeedDialDirection.down,
                children: [
                  SpeedDialChild(
                      labelWidget: Container(
                        decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(8.r)),
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CustomImageView(
                                height: 20.h,
                                imagePath: Assets.images.svg.homeFeed.svgDelete.path,
                              ),
                              buildSizedBoxW(10.0),
                              Text(
                                "Delete",
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ),
                      ),
                      onTap: () {
                        NavigatorService.goBack();
                        widget.flutterStoryController?.play();
                        context.read<HomeFeedBloc>().add(DeleteStoryEvent(storyId: widget.items[currentIndex].storyID));
                        Logger.lOG('Delete Story tapped');
                      }),
                ],
                child: Icon(
                  Icons.more_vert_rounded,
                  size: 26.sp,
                ),
              ),
            IconButton(
              onPressed: () {
                NavigatorService.goBack();
                Logger.lOG("widget.storyData.uid ${widget.storyData.uid}");
                widget.flutterStoryController?.pause();
              },
              icon: const Icon(Icons.close_rounded, color: Colors.white),
            )
          ],
        ),
      ),
    );
  }
}
