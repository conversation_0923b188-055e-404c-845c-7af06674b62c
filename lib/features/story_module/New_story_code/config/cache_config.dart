import 'package:flutter_cache_manager/flutter_cache_manager.dart';

const kStoryCacheKey = "storyCacheKey";
final kCacheManager = CacheManager(
  Config(
    kStoryCacheKey,
    stalePeriod: const Duration(days: 1), // Maximum cache duration
    maxNrOfCacheObjects: 100, // maximum reels to cache
    repo: JsonCacheInfoRepository(databaseName: kStoryCacheKey),
    fileSystem: IOFileSystem(kStoryCacheKey),
    fileService: HttpFileService(),
  ),
);

const kstoryPfImageCacheKey = "storyProfileCacheKey";
final kpfCacheManager = CacheManager(
  Config(
    kStoryCacheKey,
    stalePeriod: const Duration(days: 1), // Maximum cache duration
    repo: JsonCacheInfoRepository(databaseName: kstoryPfImageCacheKey),
    fileSystem: IOFileSystem(kstoryPfImageCacheKey),
    fileService: HttpFileService(),
  ),
);
