// NewStoryModel deserializeNewStoryModel(Map<String, dynamic> json) => NewStoryModel.fromJson(json);

// class NewStoryModel {
//   bool? status;
//   String? message;
//   List<NewStory>? data;

//   NewStoryModel({this.status, this.message, this.data});

//   NewStoryModel.fromJson(Map<String, dynamic> json) {
//     status = json['status'];
//     message = json['message'];
//     if (json['data'] != null) {
//       data = <NewStory>[];
//       json['data'].forEach((v) {
//         data!.add(NewStory.fromJson(v));
//       });
//     }
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['status'] = status;
//     data['message'] = message;
//     if (this.data != null) {
//       data['data'] = this.data!.map((v) => v.toJson()).toList();
//     }
//     return data;
//   }

//   // CopyWith method
//   NewStoryModel copyWith({
//     bool? status,
//     String? message,
//     List<NewStory>? data,
//   }) {
//     return NewStoryModel(
//       status: status ?? this.status,
//       message: message ?? this.message,
//       data: data ?? this.data,
//     );
//   }
// }

// class NewStory {
//   int? userId;
//   String? username;
//   String? userprofile;
//   List<Stories>? stories;

//   NewStory({this.userId, this.username, this.userprofile, this.stories});

//   NewStory.fromJson(Map<String, dynamic> json) {
//     userId = json['user_id'];
//     username = json['username'];
//     userprofile = json['userprofile'];
//     if (json['stories'] != null) {
//       stories = <Stories>[];
//       json['stories'].forEach((v) {
//         stories!.add(Stories.fromJson(v));
//       });
//     }
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['user_id'] = userId;
//     data['username'] = username;
//     data['userprofile'] = userprofile;
//     if (stories != null) {
//       data['stories'] = stories!.map((v) => v.toJson()).toList();
//     }
//     return data;
//   }

//   // CopyWith method
//   NewStory copyWith({
//     int? userId,
//     String? username,
//     String? userprofile,
//     List<Stories>? stories,
//   }) {
//     return NewStory(
//       userId: userId ?? this.userId,
//       username: username ?? this.username,
//       userprofile: userprofile ?? this.userprofile,
//       stories: stories ?? this.stories,
//     );
//   }
// }

// class Stories {
//   int? storyId;
//   String? title;
//   String? storytype;
//   String? storyfile;
//   bool? isLiked;

//   Stories({this.storyId, this.title, this.storytype, this.storyfile, this.isLiked});

//   Stories.fromJson(Map<String, dynamic> json) {
//     storyId = json['story_id'];
//     title = json['title'];
//     storytype = json['storytype'];
//     storyfile = json['storyfile'];
//     isLiked = json['is_liked'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['story_id'] = storyId;
//     data['title'] = title;
//     data['storytype'] = storytype;
//     data['storyfile'] = storyfile;
//     data['is_liked'] = isLiked;
//     return data;
//   }

//   // CopyWith method
//   Stories copyWith({
//     int? storyId,
//     String? title,
//     String? storytype,
//     String? storyfile,
//     bool? isLiked,
//   }) {
//     return Stories(
//       storyId: storyId ?? this.storyId,
//       title: title ?? this.title,
//       storytype: storytype ?? this.storytype,
//       storyfile: storyfile ?? this.storyfile,
//       isLiked: isLiked ?? this.isLiked,
//     );
//   }
// }
