import 'dart:math';
import 'dart:ui';

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/story_feature_demo/Story_view_demo/story_view/models/story_model.dart';
// import 'package:flowkar/features/story_module/New_story_code/new_story_model.dart';
import 'package:flowkar/features/story_module/New_story_code/src/models/story_item.dart';
import 'package:flowkar/features/story_module/New_story_code/src/models/story_view_image_config.dart';
import 'package:flowkar/features/story_module/New_story_code/src/utils/story_utils.dart';

import 'widget/moreStory.dart';

enum StoryType { highlight, story }

// ignore: must_be_immutable
class NewStoryScreen extends StatefulWidget {
  int? initialIndex;
  List<NewStory>? storydata;
  String? selectedUserId;
  StoryType? type;

  NewStoryScreen({super.key, this.initialIndex, this.storydata, this.selectedUserId, this.type});

  static Widget builder(BuildContext context) {
    final List args = ModalRoute.of(context)?.settings.arguments as List;
    return NewStoryScreen(
      initialIndex: args[0],
      storydata: args[1],
      selectedUserId: args[2],
      type: args[3],
    );
  }

  @override
  State<NewStoryScreen> createState() => _NewStoryScreenState();
}

class _NewStoryScreenState extends State<NewStoryScreen> {
  late PageController pageController;
  double currentPageValue = 0.0;

  List<StoryModel> sampleStory = [];

  @override
  void initState() {
    super.initState();

    if (widget.storydata != null) {
      sampleStory = widget.storydata!.map((data) {
        return StoryModel(
          userName: data.username ?? '',
          userProfile: data.userprofile == null || data.userprofile == ''
              ? Assets.images.pngs.other.pngPlaceholder.path
              : data.userprofile.toString(),
          uid: data.userId,
          stories: data.stories!
              .map((story) {
                if (story.storytype == 'image') {
                  return StoryItem(
                    storyItemType: StoryItemType.image,
                    storyItemSource: StoryItemSource.network,
                    url: story.storyfile,
                    storyID: story.storyId ?? 0,
                    stories: data.stories!,
                    islike: story.isLiked!,
                    duration: const Duration(seconds: 5),
                    imageConfig: StoryViewImageConfig(
                      fit: BoxFit.contain,
                      progressIndicatorBuilder: (p0, p1, p2) => Align(
                        alignment: Alignment.center,
                        child: const Center(
                          child: LoadingAnimationWidget(),
                        ),
                      ),
                    ),
                  );
                } else if (story.storytype == 'video') {
                  return StoryItem(
                    storyItemSource: StoryItemSource.network,
                    storyItemType: StoryItemType.video,
                    url: story.storyfile,
                    storyID: story.storyId ?? 0,
                    islike: story.isLiked ?? false,
                    duration: const Duration(seconds: 10),
                    // customWidget: (p0, audioPlayer) => AudioCustomView1(
                    //   controller: p0,
                    //   audioPlayer: audioPlayer,
                    // ),
                    thumbnail: const Center(child: LoadingAnimationWidget()),
                  );
                }
                return null;
              })
              .whereType<StoryItem>()
              .toList(),
        );
      }).toList();

      // ignore: curly_braces_in_flow_control_structures
      if (widget.type == StoryType.story) if (widget.selectedUserId != null) {
        int selectedIndex = sampleStory.indexWhere((story) => story.userName == widget.selectedUserId);

        if (selectedIndex != -1) {
          widget.initialIndex = selectedIndex;
        } else {
          widget.initialIndex = 0;
        }
      }
    }
    pageController = PageController(initialPage: widget.initialIndex!);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: PageView.builder(
        itemCount: sampleStory.length,
        controller: pageController,
        itemBuilder: (context, index) {
          return AnimatedBuilder(
            animation: pageController,
            child: MyStoryView(
                stdata: widget.storydata,
                storyModel: sampleStory[index],
                pageController: pageController,
                dataIndex: index,
                storydata: widget.storydata![index]),
            builder: (context, child) {
              if (pageController.position.hasContentDimensions) {
                currentPageValue = pageController.page ?? 0.0;
                final isLeaving = (index - currentPageValue) <= 0;
                final t = (index - currentPageValue);
                final rotationY = lerpDouble(0, 30, t)!;
                const maxOpacity = 0.8;
                final num opacity = lerpDouble(0, maxOpacity, t.abs())!.clamp(0.0, maxOpacity);
                final isPaging = opacity != maxOpacity;
                final transform = Matrix4.identity();
                transform.setEntry(3, 2, 0.003);
                transform.rotateY(-rotationY * (pi / 180.0));
                return Transform(
                  alignment: isLeaving ? Alignment.centerRight : Alignment.centerLeft,
                  transform: transform,
                  child: Stack(
                    children: [
                      child!,
                      if (isPaging && !isLeaving)
                        Positioned.fill(
                          child: Opacity(opacity: opacity as double, child: const ColoredBox(color: Colors.black87)),
                        )
                    ],
                  ),
                );
              }

              return child!;
            },
          );
        },
      ),
    );
  }
}

// Custom Story Data Model
class StoryModel {
  String userName;
  String userProfile;
  List<StoryItem> stories;
  int? uid;

  StoryModel({required this.userName, required this.userProfile, required this.stories, this.uid});
}
