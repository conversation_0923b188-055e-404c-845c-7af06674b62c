import 'package:flowkar/core/helpers/vibration_helper.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/user_management/bloc/user_management_bloc.dart';
import 'package:flowkar/features/user_management/page/search_user_management_screen.dart';
import 'package:flowkar/features/user_management/widget/user_manage_container_widget.dart';
import 'package:flowkar/features/user_management/widget/user_management_screen_shimmer.dart';
import 'package:flowkar/features/widgets/custom/custom_conditional_scroll_physics.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';

class UserManagementScreen extends StatefulWidget {
  const UserManagementScreen({super.key});
  static Widget builder(BuildContext context) {
    return UserManagementScreen();
  }

  @override
  State<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends State<UserManagementScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    context.read<UserManagementBloc>().add(GetInvitedUserAPIEvent());
    context.read<UserManagementBloc>().add(GetInviteeUserAPIEvent());
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 2.5.w),
        child: Scaffold(
          appBar: _buildUserManageAppBar(context),
          floatingActionButton: FloatingActionButton(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(100.r)),
            onPressed: () {
              PersistentNavBarNavigator.pushNewScreen(context, screen: SearchUserManagementScreen());
            },
            child: Icon(
              Icons.add_rounded,
              size: 26.sp,
            ),
          ),
          body: BlocListener<ConnectivityBloc, ConnectivityState>(
            listener: (context, connectivityState) {
              if (connectivityState.isReconnected) {
                context.read<UserManagementBloc>().add(GetInvitedUserAPIEvent());
                context.read<UserManagementBloc>().add(GetInviteeUserAPIEvent());
              }
            },
            child: BlocBuilder<UserManagementBloc, UserManagementState>(
              builder: (context, state) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTabBar(),
                    buildSizedBoxH(16.0),
                    Expanded(
                      child: LiquidPullToRefresh(
                        color: Theme.of(context).primaryColor.withOpacity(0.5),
                        showChildOpacityTransition: false,
                        backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
                        onRefresh: () async {
                          context.read<UserManagementBloc>().add(GetInvitedUserAPIEvent());
                          context.read<UserManagementBloc>().add(GetInviteeUserAPIEvent());
                          return Future.value();
                        },
                        child: TabBarView(
                          controller: _tabController,
                          children: [
                            BlocBuilder<ConnectivityBloc, ConnectivityState>(
                              builder: (context, connectivityState) {
                                if (!connectivityState.isConnected &&
                                    (state.getInvitedUserModel?.data?.isEmpty ?? true)) {
                                  return ManageUserShimmer(
                                      height: 80.h, padding: EdgeInsets.symmetric(horizontal: 16.w));
                                } else if (state.getInvitedUserLoading) {
                                  return ManageUserShimmer(
                                      height: 80.h, padding: EdgeInsets.symmetric(horizontal: 16.w));
                                } else if (state.getInvitedUserModel?.data?.isEmpty ?? true) {
                                  return _buildNoInvitedUserFound();
                                } else {
                                  return ListView.builder(
                                    shrinkWrap: true,
                                    controller: _scrollController,
                                    physics: ConditionalAlwaysScrollPhysics(controller: _scrollController),
                                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                                    itemCount: state.getInvitedUserModel?.data?.length,
                                    itemBuilder: (context, index) {
                                      var user = state.getInvitedUserModel?.data?[index];

                                      return UserManageContainer(
                                        invitedUserData: user,
                                        tabIndex: 0,
                                        brandLogos: user?.brands?.map((brand) {
                                              Logger.lOG("${APIConfig.mainbaseURL}${user.profileImage}");
                                              return {
                                                "name": brand.name ?? "",
                                                "logo": (brand.logo?.startsWith('/') ?? false)
                                                    ? "${APIConfig.mainbaseURL}${brand.logo}"
                                                    : AssetConstants.pngPlaceholder,
                                              };
                                            }).toList() ??
                                            [],
                                        email: user?.email ?? "",
                                        imageUrl: user?.profileImage?.isNotEmpty == true || user?.profileImage != ""
                                            ? "${APIConfig.mainbaseURL}${user?.profileImage}"
                                            : AssetConstants.pngUserReomve,
                                      );
                                    },
                                  );
                                }
                              },
                            ),
                            BlocBuilder<ConnectivityBloc, ConnectivityState>(
                              builder: (context, connectivityState) {
                                if (!connectivityState.isConnected &&
                                    (state.getInviteeUserModel?.data?.isEmpty ?? true)) {
                                  return ManageUserShimmer(
                                      height: 80.h, padding: EdgeInsets.symmetric(horizontal: 16.w));
                                } else if (state.getInviteeUserLoading) {
                                  return ManageUserShimmer(
                                      height: 80.h, padding: EdgeInsets.symmetric(horizontal: 16.w));
                                } else if (state.getInviteeUserModel?.data?.isEmpty ?? true) {
                                  return _buildNoInviteeUserFound();
                                } else {
                                  return ListView.builder(
                                    controller: _scrollController,
                                    physics: ConditionalAlwaysScrollPhysics(controller: _scrollController),
                                    padding: EdgeInsets.symmetric(horizontal: 14.5.w),
                                    itemCount: state.getInviteeUserModel?.data?.length,
                                    itemBuilder: (context, index) {
                                      var user = state.getInviteeUserModel?.data?[index];

                                      return UserManageContainer(
                                        inviteeUserData: user,
                                        tabIndex: 1,
                                        brandLogos: user?.brands?.map((brand) {
                                              Logger.lOG("${APIConfig.mainbaseURL}${user.profileImage}");
                                              return {
                                                "name": brand.name ?? "",
                                                "logo": (brand.logo?.startsWith('/') ?? false)
                                                    ? "${APIConfig.mainbaseURL}${brand.logo}"
                                                    : AssetConstants.pngPlaceholder,
                                              };
                                            }).toList() ??
                                            [],
                                        email: user?.email ?? "",
                                        imageUrl: user?.profileImage?.isNotEmpty == true || user?.profileImage != ""
                                            ? "${APIConfig.mainbaseURL}${user?.profileImage}"
                                            : AssetConstants.pngUserReomve,
                                      );
                                    },
                                  );
                                }
                              },
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNoInvitedUserFound() {
    return ListView(
      physics: AlwaysScrollableScrollPhysics(),
      children: [
        buildSizedBoxH(MediaQuery.of(context).size.height / 6),
        ExceptionWidget(
          imagePath: Assets.images.svg.other.svgNodatafound.path,
          // Assets.images.pngs.exception.pngNoDataFound.path,
          showButton: false,
          title: 'No Invited User Found',
          subtitle: 'You have not invited any user yet.',
        ),
      ],
    );
  }

  Widget _buildNoInviteeUserFound() {
    return ListView(
      physics: AlwaysScrollableScrollPhysics(),
      children: [
        buildSizedBoxH(MediaQuery.of(context).size.height / 6),
        ExceptionWidget(
          imagePath: Assets.images.svg.other.svgNodatafound.path,
          showButton: false,
          title: 'No Invitee User Found',
          subtitle: 'You have not invitee any user yet.',
        ),
      ],
    );
  }

  PreferredSizeWidget _buildUserManageAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            PersistentNavBarNavigator.pop(context);
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          Lang.current.lbl_user_management,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      height: 50.h,
      margin: EdgeInsets.symmetric(horizontal: 60.w, vertical: 8.h),
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: const Color(0xffF0EFEF),
        borderRadius: BorderRadius.circular(25.r),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.black,
        dividerColor: Colors.transparent,
        indicatorSize: TabBarIndicatorSize.tab,
        overlayColor: WidgetStateColor.transparent,
        onTap: (value) {
          VibrationHelper.singleShortBuzz();
        },
        indicator: BoxDecoration(
          color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(25),
        ),
        labelStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700, fontSize: 16.sp),
        unselectedLabelStyle:
            Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700, fontSize: 16.sp),
        tabs: const [
          Tab(text: "Invited"),
          Tab(text: "Invitee"),
        ],
      ),
    );
  }
}
