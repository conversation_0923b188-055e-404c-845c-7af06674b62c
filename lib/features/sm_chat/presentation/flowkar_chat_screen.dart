import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flowkar/core/socket/socket_service.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/string_extension.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/home_feed_screen/model/post_response_model.dart';
import 'package:flowkar/features/notification/widget/get_post_by_id_screen.dart';
import 'package:flowkar/features/reels_screen/presentation/pages/video_page.dart';
import 'package:flowkar/features/reels_screen/service/reel_service.dart';
import 'package:flowkar/features/sm_chat/bloc/sm_chat_bloc.dart';
import 'package:flowkar/features/sm_chat/model/flowkar_chat_model/chat_message_list_model.dart';
import 'package:flowkar/features/sm_chat/widget/link_preview.dart';
import 'package:flowkar/features/sm_chat/widget/sm_chat_screen_shimmer.dart';
import 'package:flowkar/features/sm_chat/widget/typing_indicator_widget.dart';
import 'package:flowkar/features/widgets/common/get_user_profile_by_id/get_user_profile_by_id.dart';
import 'package:flowkar/features/widgets/custom/custom_debounce.dart';
import 'package:flutter/cupertino.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';

enum TypeWriterStatus { typing, typed }

class FlowkarChatScreen extends StatefulWidget {
  final bool? isDeepLink;
  final dynamic args;

  const FlowkarChatScreen({super.key, this.args, this.isDeepLink});

  static Widget builder(BuildContext context, {bool? isDeepLink}) {
    var args = ModalRoute.of(context)?.settings.arguments;
    return FlowkarChatScreen(args: args, isDeepLink: isDeepLink);
    // BlocProvider<SmChatBloc>(
    //   create: (context) =>
    //       SmChatBloc(const SmChatState(), ChatRepository(apiClient: ApiClient()))
    //         ..add(ChatInitialEvent()),
    //   child: FlowkarChatScreen(args: args),
    // );
  }

  @override
  State<FlowkarChatScreen> createState() => _FlowkarChatScreenState();
}

class _FlowkarChatScreenState extends State<FlowkarChatScreen> with WidgetsBindingObserver {
  ScrollController? _scrollController;
  bool _showScrollToBottomButton = false;
  final ValueNotifier<String> _inputText = ValueNotifier('');
  ValueNotifier<TypeWriterStatus> composingStatus = ValueNotifier(TypeWriterStatus.typed);
  final ImagePicker _imagePicker = ImagePicker();
  // RecorderController? controller;
  ValueNotifier<bool> isRecording = ValueNotifier(false);
  late Debouncer debouncer;
  final ValueNotifier<bool> _showTypingIndicator = ValueNotifier(false);
  ValueListenable<bool> get typingIndicatorNotifier => _showTypingIndicator;
  int lastMassageID = 0;

  bool _isStart = true;
  bool _hasCalledMessageRead = false;
  final FocusNode _chatFocusNode = FocusNode();

  bool isKeyPadOpen = false;

  @override
  @override
  void initState() {
    _chatFocusNode.addListener(() {
      if (_chatFocusNode.hasFocus) {
        isKeyPadOpen = true;
      }
    });
    // Add app lifecycle observer
    WidgetsBinding.instance.addObserver(this);

    SocketService.emit(APIConfig.joinSocket, {
      'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
    });
    SocketService.response(APIConfig.joinSocket, (joinSocket) {
      Logger.lOG(joinSocket);
    });
    debouncer = Debouncer(const Duration(seconds: 1));

    widget.isDeepLink == true
        ? context.read<SmChatBloc>().add(GetChatMessageListEvent(page: 1, userId: widget.args.userId.toString()))
        : scheduleMicrotask(() => context
            .read<SmChatBloc>()
            .add(GetChatMessageListEvent(page: 1, userId: widget.args?['item'].userId.toString() ?? "")));

    super.initState();
    final state = context.read<SmChatBloc>().state;
    if (state.chatController?.text.isNotEmpty ?? true) {
      state.chatController?.clear();
    }
    _scrollController = ScrollController()..addListener(_scrollListener);

    if (context.read<SmChatBloc>().state.chatMessageList.isNotEmpty) {
      context.read<SmChatBloc>().state.chatMessageList.clear();
    }

    // Set up socket listeners for this specific chat with a small delay to ensure socket is ready
    Future.delayed(Duration(milliseconds: 500), () {
      if (mounted) {
        Logger.lOG("FLOWKAR_CHAT: Setting up socket listeners, isMessageScreen: ${isMessageScreen.value}");
        _setupSocketListeners();
        // Check if the user we're chatting with is currently typing
        _checkInitialTypingState();
      }
    });

    if (defaultTargetPlatform == TargetPlatform.iOS || defaultTargetPlatform == TargetPlatform.android) {
      // controller = RecorderController();
    }
    _scrollController?.addListener(() {
      if (_scrollController!.offset > 300) {
        // If the user scrolls up 300px
        setState(() {
          _showScrollToBottomButton = true;
        });
      } else {
        setState(() {
          _showScrollToBottomButton = false;
        });
      }
    });
  }

  // @override
  // void didChangeAppLifecycleState(AppLifecycleState state) {
  //   if (state == AppLifecycleState.paused) {
  //     // App is in the background
  //     SocketService.closeConnection();
  //   } else if (state == AppLifecycleState.resumed) {
  //     // App is in the foreground
  //     SocketService.initializeSocket();
  //     Future.delayed(const Duration(seconds: 2), () {
  //       Logger.lOG("RECONNECT");
  //       if (Prefobj.preferences?.get(Prefkeys.AUTHTOKEN) != null) {
  //         SocketService.emit(APIConfig.joinSocket, {
  //           'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
  //         });
  //         SocketService.response(
  //           APIConfig.joinSocket,
  //           (joinSocket) {},
  //         );
  //       }
  //       _setupSocketListeners();
  //       _checkInitialTypingState();
  //     });
  //   }
  // }

  void _setupSocketListeners() {
    // Get current user ID and the user whose chat is currently open
    final currentUserId = int.tryParse(Prefobj.preferences?.get(Prefkeys.USER_ID).toString() ?? '0') ?? 0;
    final chatWithUserId = widget.isDeepLink == true ? widget.args.userId : widget.args?['item'].userId ?? 0;

    Logger.lOG(
        "FLOWKAR_CHAT: Setting up socket listeners for chat with user: $chatWithUserId, current user: $currentUserId");

    // Set up typing indicator listener
    SocketService.response(
      APIConfig.isTyping,
      (response) {
        Logger.lOG("FLOWKAR_CHAT: Raw typing response: $response");

        if (mounted) {
          final typingUserId = response['user_id'];
          final isTyping = response['is_typing'] ?? false;

          Logger.lOG(
              "FLOWKAR_CHAT: Processing typing - UserId: $typingUserId, IsTyping: $isTyping, ChatWith: $chatWithUserId, isMessageScreen: ${isMessageScreen.value}, mounted: $mounted");

          // Always update global typing state
          if (isTyping) {
            globalTypingUsers[typingUserId] = true;
          } else {
            globalTypingUsers.remove(typingUserId);
          }

          // Show typing indicator if the typing user is the one we're chatting with
          if (typingUserId == chatWithUserId) {
            Logger.lOG("FLOWKAR_CHAT: Setting typing indicator to: $isTyping for user: $typingUserId");
            _showTypingIndicator.value = isTyping;
          }
        }
      },
    );

    // Set up receive message listener
    SocketService.response(APIConfig.receivemessage, (response) {
      if (mounted) {
        final id = response['id'];
        final message = response['message'];
        final type = response['type'];
        final createdat = response['created_at'];
        final sentby = response['sent_by'];

        Logger.lOG(
            "FLOWKAR_CHAT: Processing message - SentBy: $sentby, CurrentUser: $currentUserId, ChatWith: $chatWithUserId, Message: $message, isMessageScreen: ${isMessageScreen.value}, mounted: $mounted");

        // Only process message if it belongs to current chat conversation
        // For received messages, we should only show them if:
        // The message was sent by the user whose chat is currently open to the current user
        bool isMessageForCurrentChat = (sentby == chatWithUserId);

        if (isMessageForCurrentChat) {
          Logger.lOG("FLOWKAR_CHAT: Processing message for current chat");
          // Clear typing indicator when message is received
          _showTypingIndicator.value = false;
          globalTypingUsers.remove(sentby);

          context.read<SmChatBloc>().add(
              UpdateChatMessageSocketEvent(id: id, createdat: createdat, message: message, sentby: sentby, type: type));

          if (_isStart) {
            Future.delayed(
              Duration(seconds: 2),
              () {
                if (sentby.toString() != Prefobj.preferences?.get(Prefkeys.USER_ID).toString()) {
                  Logger.lOG("touser ${touser.value}");
                  SocketService.emit(APIConfig.messageRead, {
                    'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
                    'to': Prefobj.preferences?.get(Prefkeys.USER_ID),
                    'message_id': lastMassageID,
                  });
                }
              },
            );
            _isStart = false;
          }
          setState(() {});
        } else {
          Logger.lOG(
              "FLOWKAR_CHAT: Received message filtered out - not for current chat. SentBy: $sentby, CurrentUser: $currentUserId, ChatWith: $chatWithUserId, isMessageScreen: ${isMessageScreen.value}");
        }
      }
    });

    // Set up send message listener
    SocketService.response(
      APIConfig.sendmessage,
      (response) {
        if (mounted) {
          final id = response['id'];
          final message = response['message'];
          final type = response['type'];
          final createdat = response['created_at'];
          final sentby = response['sent_by'];

          Logger.lOG(
              "FLOWKAR_CHAT: Processing sent message - SentBy: $sentby, CurrentUser: $currentUserId, isMessageScreen: ${isMessageScreen.value}, mounted: $mounted");

          // Only process message if it belongs to current chat conversation
          // For sent messages, we should only show them if:
          // The current user sent the message (in the context of this chat)
          bool isMessageForCurrentChat = (sentby == currentUserId);

          if (isMessageForCurrentChat) {
            Logger.lOG("FLOWKAR_CHAT: Processing sent message for current chat");
            context.read<SmChatBloc>().add(UpdateChatMessageSocketEvent(
                id: id, createdat: createdat, message: message, sentby: sentby, type: type));
            setState(() {});
          } else {
            Logger.lOG(
                "FLOWKAR_CHAT: Sent message filtered out - not for current chat. SentBy: $sentby, CurrentUser: $currentUserId, ChatWith: $chatWithUserId, isMessageScreen: ${isMessageScreen.value}");
          }
        }
      },
    );

    // Set up delete message listener
    SocketService.response(APIConfig.deleteMessage, (response) {
      if (mounted) {
        final messageId = response['message_id'];
        final success = response['status'] ?? false;

        if (success) {
          // Always remove the message when we get a successful delete response
          // This ensures the message is removed from all users' screens
          context.read<SmChatBloc>().add(RemoveDeletedMessageEvent(messageId: messageId));
        }
      }
    });
  }

  void _checkInitialTypingState() {
    final chatWithUserId = widget.isDeepLink == true ? widget.args.userId : widget.args?['item'].userId ?? 0;

    // Check if the user we're chatting with is currently typing
    if (globalTypingUsers.containsKey(chatWithUserId) && globalTypingUsers[chatWithUserId] == true) {
      Logger.lOG("FLOWKAR_CHAT: User $chatWithUserId was already typing, showing indicator");
      _showTypingIndicator.value = true;
    }
  }

  @override
  void dispose() {
    // Remove app lifecycle observer
    WidgetsBinding.instance.removeObserver(this);

    _scrollController?.dispose();
    _isStart = false;
    _showTypingIndicator.value = false;
    isMessageScreen.value = false;
    massageUserID.value = 0;
    touser.value = 0;

    // Clean up socket listeners to prevent conflicts
    // Note: SocketService.response() automatically removes previous listeners
    // but we ensure typing indicator is reset
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController?.position.pixels == _scrollController?.position.maxScrollExtent) {
      final state = context.read<SmChatBloc>().state;
      if (!state.isLoadingMore) {
        widget.isDeepLink == true
            ? context
                .read<SmChatBloc>()
                .add(GetChatMessageListEvent(page: state.page + 1, userId: widget.args.userId.toString()))
            : scheduleMicrotask(() => context.read<SmChatBloc>().add(
                GetChatMessageListEvent(page: state.page + 1, userId: widget.args?['item'].userId.toString() ?? "")));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    isMessageScreen.value = true;

    // Ensure socket listeners are set up for individual chat screen
    // if (!_hasSetupListeners) {
    //   _hasSetupListeners = true;
    //   Logger.lOG("FLOWKAR_CHAT: Setting up socket listeners from build method");
    //   Future.delayed(Duration(milliseconds: 100), () {
    //     if (mounted) {
    //       _setupSocketListeners();
    //     }
    //   });
    // }

    return WillPopScope(
      onWillPop: () async {
        FocusScope.of(context).requestFocus(FocusNode());
        isMessageScreen.value = false;
        massageUserID.value = 0;
        touser.value = 0;
        _showTypingIndicator.value = false;

        NavigatorService.goBack();
        // context.read<SmChatBloc>().add(GetChatListEvent(page: 1, isReload: true));
        context.read<SmChatBloc>().add(GetSmchatListEvent());
        context.read<SmChatBloc>().add(GetTelegramUerListAPI());

        widget.args['callback']();
        return true;
      },
      child: BlocListener<ConnectivityBloc, ConnectivityState>(
        listener: (context, state) {
          if (state.isReconnected) {
            widget.isDeepLink == true
                ? context
                    .read<SmChatBloc>()
                    .add(GetChatMessageListEvent(page: 1, userId: widget.args.userId.toString()))
                : scheduleMicrotask(() => context
                    .read<SmChatBloc>()
                    .add(GetChatMessageListEvent(page: 1, userId: widget.args?['item'].userId.toString() ?? "")));
          }
        },
        child: BlocBuilder<SmChatBloc, SmChatState>(
          builder: (context, state) {
            return BlocBuilder<ThemeBloc, ThemeState>(
              builder: (context, themeState) {
                // only first time call this socket

                Future.delayed(
                  Duration(seconds: 0),
                  () {
                    if (touser.value.toString() == Prefobj.preferences?.get(Prefkeys.USER_ID).toString()) {
                      Logger.lOG("touser ${touser.value}");
                      SocketService.emit(APIConfig.messageRead, {
                        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
                        'to': touser.value,
                        'message_id': state.chatMessageList.first.id,
                      });
                      // context.read<SmChatBloc>().add(const GetChatListEvent(page: 1));
                    }
                  },
                );
                if (!_hasCalledMessageRead &&
                    state.chatMessageList.isNotEmpty &&
                    touser.value.toString() == Prefobj.preferences?.get(Prefkeys.USER_ID).toString()) {
                  _hasCalledMessageRead = true;

                  // Store context reference before async operation
                  final bloc = context.read<SmChatBloc>();
                  Future.delayed(
                    Duration(seconds: 0),
                    () {
                      if (mounted) {
                        Logger.lOG("touser ${touser.value}");
                        SocketService.emit(APIConfig.messageRead, {
                          'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
                          'to': touser.value,
                          'message_id': state.chatMessageList.first.id,
                        });

                        bloc.add(const GetChatListEvent(page: 1, isReload: true));
                      }
                    },
                  );
                }

                return Scaffold(
                  body: SafeArea(
                    child: Stack(
                      alignment: Alignment.bottomCenter / 1.3.h,
                      children: [
                        Column(
                          children: [
                            _buildchatAppBar(context, themeState, widget.args),
                            Expanded(
                              child: InkWell(
                                focusColor: Colors.transparent,
                                onTap: () {
                                  FocusManager.instance.primaryFocus?.unfocus();
                                },
                                child: Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 16.0.w),
                                  child: Column(
                                    children: [
                                      Visibility(
                                        visible: state.isLoadingMore,
                                        child: SizedBox(
                                          height: 50.h,
                                          child: Center(
                                              child: CupertinoActivityIndicator(
                                                  color: Theme.of(context).colorScheme.primary)),
                                        ),
                                      ),
                                      Expanded(
                                        child: BlocBuilder<ConnectivityBloc, ConnectivityState>(
                                          builder: (context, connectivityState) {
                                            if (!connectivityState.isConnected && state.chatMessageList.isEmpty) {
                                              return ChatDetailShimmer();
                                            } else if (state.isloding) {
                                              return ChatDetailShimmer();
                                            } else if (state.chatMessageList.isEmpty) {
                                              return FutureBuilder(
                                                future: Future.delayed(Duration(seconds: 3)),
                                                builder: (context, snapshot) {
                                                  if (snapshot.connectionState != ConnectionState.done) {
                                                    return isKeyPadOpen
                                                        ? ListView(
                                                            physics: AlwaysScrollableScrollPhysics(),
                                                            children: [
                                                              buildSizedBoxH(MediaQuery.of(context).size.height / 8),
                                                              ExceptionWidget(
                                                                imagePath:
                                                                    Assets.images.svg.exception.svgNodatafound.path,
                                                                showButton: false,
                                                                title: Lang.of(context).lbl_no_data_found,
                                                                subtitle: Lang.of(context).lbl_no_message,
                                                              ),
                                                            ],
                                                          )
                                                        : ChatDetailShimmer();
                                                  }

                                                  // Show ExceptionWidget after delay
                                                  return ListView(
                                                    physics: AlwaysScrollableScrollPhysics(),
                                                    children: [
                                                      buildSizedBoxH(MediaQuery.of(context).size.height / 8),
                                                      ExceptionWidget(
                                                        imagePath: Assets.images.svg.exception.svgNodatafound.path,
                                                        showButton: false,
                                                        title: Lang.of(context).lbl_no_data_found,
                                                        subtitle: Lang.of(context).lbl_no_message,
                                                      ),
                                                    ],
                                                  );
                                                },
                                              );
                                            } else {
                                              return _buildMessageList(state.chatMessageList, themeState);
                                            }
                                          },
                                        ),
                                      ),
                                      buildSizedBoxH(20.0),
                                      ValueListenableBuilder(
                                        valueListenable: typingIndicatorNotifier,
                                        builder: (context, value, child) => TypingIndicator(
                                            showIndicator: value,
                                            userrname: widget.isDeepLink == true
                                                ? widget.args.username
                                                : widget.args?['item'].userName.toString() ?? ""),
                                      ),
                                      if (state.aiGenerateMassageOrCommentModel?.result != null &&
                                          (state.aiGenerateMassageOrCommentModel?.result.isNotEmpty ?? false))
                                        _buildAIResponseSection(),
                                      _buildMessageTextField(state, themeState),
                                      Platform.isIOS ? buildSizedBoxH(30.0) : buildSizedBoxH(20.0),
                                    ],
                                  ),
                                ),
                              ),
                            )
                          ],
                        ),
                        if (_showScrollToBottomButton)
                          FloatingActionButton(
                            mini: true,
                            backgroundColor: Theme.of(context).primaryColor,
                            onPressed: () {
                              _scrollToBottom();
                            },
                            child: const Icon(Icons.arrow_downward),
                          ),
                      ],
                    ),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  PreferredSizeWidget _buildchatAppBar(BuildContext context, ThemeState themeState, dynamic args) {
    final profileImage =
        widget.isDeepLink == true ? args.profileImage.toString() : args?['item']?.profileImage?.toString();

    return CustomAppbar(
      leading: [
        InkWell(
          onTap: () {
            setState(() {
              FocusScope.of(context).requestFocus(FocusNode());
              isMessageScreen.value = false;
              massageUserID.value = 0;
              touser.value = 0;
            });
            // context.read<SmChatBloc>().add(GetChatListEvent(page: 1, isReload: true));
            context.read<SmChatBloc>().add(GetSmchatListEvent());
            context.read<SmChatBloc>().add(GetTelegramUerListAPI());

            NavigatorService.goBack();
            widget.args['callback']();
          },
          child: CustomImageView(
            imagePath: AssetConstants.pngBack,
            height: 16.0.h,
            margin: EdgeInsets.only(top: 19.0.h, bottom: 19.0.w, left: 10.0.w),
          ),
        ),
        buildSizedBoxW(20.w),
        InkWell(
          onTap: () {
            PersistentNavBarNavigator.pushNewScreen(context,
                screen: GetUserProfileById(
                    userId: widget.isDeepLink == true ? widget.args.userId : args?['item'].userId,
                    stackonScreen: false),
                withNavBar: false);
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // ClipRRect(
              //   clipBehavior: Clip.hardEdge,
              //   child: Padding(
              //     padding: const EdgeInsets.all(4.0),
              //     child: CustomImageView(
              //       radius: BorderRadius.circular(25.r),
              //       height: 40.0.h,
              //       width: 40.0.w,
              //       fit: BoxFit.cover,
              //       imagePath: (profileImage == null || profileImage.isEmpty)
              //           ? AssetConstants.pngUserReomve
              //           : profileImage.toString().startsWith('/media/')
              //               ? "${APIConfig.mainbaseURL}$profileImage"
              //               : profileImage,
              //       alignment: Alignment.center,
              //     ),
              //   ),
              // ),
              Container(
                padding: const EdgeInsets.all(2.0),
                clipBehavior: Clip.antiAlias,
                decoration: BoxDecoration(
                  border: Border.all(color: Theme.of(context).customColors.messagecolor),
                  borderRadius: BorderRadius.circular(100.r),
                ),
                child: CustomImageView(
                  radius: BorderRadius.circular(100.r),
                  height: 40.0.h,
                  width: 40.0.w,
                  fit: BoxFit.cover,
                  imagePath: (profileImage == null || profileImage.isEmpty)
                      ? AssetConstants.pngUserReomve
                      : profileImage.toString().startsWith('/media/')
                          ? "${APIConfig.mainbaseURL}$profileImage"
                          : profileImage,
                  alignment: Alignment.center,
                ),
              ),
              buildSizedBoxW(8),
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(widget.isDeepLink == true ? widget.args.username : args?['item'].name.toString() ?? "",
                      style: Theme.of(context)
                          .textTheme
                          .bodyMedium
                          ?.copyWith(fontWeight: FontWeight.w700, fontSize: 16.0.sp)),
                ],
              )
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMessageList(List<ChatMessageData> messages, ThemeState themeState) {
    final groupedMessages = _groupMessagesByDate(messages);

    return ListView.builder(
      padding: EdgeInsets.zero,
      controller: _scrollController,
      itemCount: groupedMessages.length,
      reverse: true,
      shrinkWrap: true,
      itemBuilder: (context, index) {
        final date = groupedMessages.keys.elementAt(index);
        final messages = groupedMessages[date]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(date.toString(),
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontSize: 11.0.sp)),
                ),
              ],
            ),
            ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              reverse: true,
              itemCount: messages.length,
              itemBuilder: (context, messageIndex) {
                lastMassageID = messages.first.id ?? 0;
                return _buildChatBubble(messages[messageIndex], themeState);
              },
            ),
          ],
        );
      },
    );
  }

  void _scrollToBottom() {
    _scrollController?.animateTo(
      0.0, // Scroll to the top as the list is reversed
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _showDeleteMessageDialog(ChatMessageData message) async {
    await showDialog(
      context: context,
      builder: (context) => CustomAlertDialog(
        title: 'Delete Message',
        subtitle: 'Are you sure you want to delete this message?',
        confirmButtonText: "Delete",
        onCancelButtonPressed: () => Navigator.pop(context, false),
        onConfirmButtonPressed: () {
          Navigator.of(context).pop();
          context.read<SmChatBloc>().add(DeleteMessageEvent(messageId: message.id ?? 0));
        },
      ),
    );
  }

  Widget _buildChatBubble(ChatMessageData message, ThemeState themeState) {
    final profileImage =
        widget.isDeepLink == true ? widget.args.profileImage : widget.args?['item'].profileImage.toString();
    final isCurrentUser = message.sentBy == int.tryParse(Prefobj.preferences?.get(Prefkeys.USER_ID));

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: isCurrentUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          Visibility(
            visible: !isCurrentUser,
            child: Container(
              padding: const EdgeInsets.all(2.0),
              clipBehavior: Clip.antiAlias,
              decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).customColors.messagecolor),
                borderRadius: BorderRadius.circular(100.r),
              ),
              child: CustomImageView(
                radius: BorderRadius.circular(100.r),
                height: 40.0.h,
                width: 40.0.w,
                fit: BoxFit.cover,
                imagePath: (profileImage == null || profileImage.isEmpty)
                    ? AssetConstants.pngUserReomve
                    : profileImage.toString().startsWith('/media/')
                        ? "${APIConfig.mainbaseURL}$profileImage"
                        : profileImage,
                alignment: Alignment.center,
              ),
            ),
          ),
          GestureDetector(
            onLongPress: () {
              // Only show delete option for messages sent by current user
              if (isCurrentUser) {
                _showDeleteMessageDialog(message);
              }
            },
            child: message.type == "share_post"
                ? (message.postError != null &&
                        message.postError!
                            .contains("Post not found or invalid reference: Post matching query does not exist."))
                    ? Container(
                        width: 250.0.w,
                        padding: EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(isCurrentUser ? 10.r : 0.r),
                            topRight: Radius.circular(10.r),
                            bottomRight: Radius.circular(isCurrentUser ? 0.r : 10.r),
                            bottomLeft: Radius.circular(10.r),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Message unavailable",
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Theme.of(context).primaryColor,
                                    fontSize: 13.0.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                            Text(
                              "This content may have been deleted by its owner or hidden by their privacy settings.",
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Theme.of(context).customColors.black,
                                    fontSize: 13.0.sp,
                                  ),
                            ),
                          ],
                        ),
                      )
                    : Column(
                        children: [
                          Container(
                            width: 250.0.w,
                            margin: EdgeInsets.only(
                                right:
                                    message.sentBy != int.tryParse(Prefobj.preferences?.get(Prefkeys.USER_ID)) ? 0 : 1),
                            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                            decoration: BoxDecoration(
                              color: Theme.of(context).customColors.white,
                              boxShadow: [
                                BoxShadow(
                                  color: Theme.of(context).customColors.black.withOpacity(0.2),
                                  blurRadius: 2,
                                )
                              ],
                              borderRadius:
                                  BorderRadius.only(topLeft: Radius.circular(10.r), topRight: Radius.circular(10.r)),
                            ),
                            child: Row(
                              children: [
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    ClipRRect(
                                      child: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Container(
                                          height: 35.0.h,
                                          width: 35.0.w,
                                          padding: (message.postUser?.profileImage == AssetConstants.pngUser ||
                                                      (message.postUser?.profileImage.isEmpty ?? true) ||
                                                      message.postUser?.profileImage == '') &&
                                                  message.postUser?.id.toString() ==
                                                      Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                                              ? EdgeInsets.all(8)
                                              : EdgeInsets.all(2),
                                          // padding: widget.profileImage.isEmpty ? EdgeInsets.all(10) : EdgeInsets.zero,
                                          decoration: BoxDecoration(
                                              border: Border.all(
                                                  color: Theme.of(context).primaryColor.withOpacity(0.2), width: 2),
                                              // borderRadius: BorderRadius.circular(100.r),
                                              shape: BoxShape.circle),
                                          child: CustomImageView(
                                            onTap: () {
                                              if (message.postUser?.id.toString() ==
                                                  Prefobj.preferences?.get(Prefkeys.USER_ID)) {
                                                PersistentNavBarNavigator.pushNewScreen(context,
                                                    screen: UserProfileScreen(), withNavBar: false);

                                                // NavigatorService.pushNamed(AppRoutes.userProfileScreen);
                                              } else {
                                                PersistentNavBarNavigator.pushNewScreen(context,
                                                    screen: GetUserProfileById(
                                                        userId: message.postUser?.id, stackonScreen: false),
                                                    withNavBar: false);
                                              }
                                            },
                                            radius: message.postUser?.id.toString() ==
                                                    Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                                                ? (message.postUser?.profileImage.isEmpty ?? true)
                                                    ? null
                                                    : BorderRadius.circular(100.0)
                                                : BorderRadius.circular(100.0),
                                            // radius: widget.profileImage.isEmpty ? null : BorderRadius.circular(100.r),
                                            fit: BoxFit.cover,
                                            imagePath: (message.postUser?.profileImage != null &&
                                                    (message.postUser?.profileImage.isEmpty ?? true))
                                                ? message.postUser?.id.toString() ==
                                                        Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                                                    ? AssetConstants.pngUser
                                                    : Assets.images.pngs.other.pngPlaseholder2.path
                                                : (message.postUser?.profileImage != null &&
                                                        (message.postUser?.profileImage.startsWith('/media/') ?? false))
                                                    ? "${APIConfig.mainbaseURL}${message.postUser?.profileImage}"
                                                    : message.postUser?.profileImage,
                                            alignment: (message.postUser?.profileImage != null &&
                                                    (message.postUser?.profileImage.isEmpty ?? true))
                                                ? Alignment.center
                                                : null,
                                          ),
                                        ),
                                      ),
                                    ),
                                    buildSizedBoxW(4),
                                    InkWell(
                                      onTap: () {
                                        if (message.postUser?.id.toString() ==
                                            Prefobj.preferences?.get(Prefkeys.USER_ID)) {
                                          PersistentNavBarNavigator.pushNewScreen(context,
                                              screen: UserProfileScreen(), withNavBar: false);
                                          // NavigatorService.pushNamed(AppRoutes.userProfileScreen);
                                        } else {
                                          PersistentNavBarNavigator.pushNewScreen(context,
                                              screen: GetUserProfileById(
                                                  userId: message.postUser?.id, stackonScreen: false),
                                              withNavBar: false);
                                        }
                                      },
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            message.postUser?.name ?? "",
                                            style: Theme.of(context).textTheme.titleLarge!.copyWith(
                                                fontSize: 12.sp, fontWeight: FontWeight.w700, color: Color(0xff292D32)),
                                          ),
                                          buildSizedBoxH(1),
                                          Text(
                                            "@${message.postUser?.username ?? ""}",
                                            style: Theme.of(context).textTheme.titleLarge!.copyWith(
                                                fontSize: 10.sp, fontWeight: FontWeight.w500, color: Color(0xff292D32)),
                                          ),
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                              ],
                            ),
                          ),
                          ((message.title != null && message.title!.isNotEmpty) ||
                                  (message.description != null && message.description!.isNotEmpty))
                              ? InkWell(
                                  onTap: () {
                                    if (message.postUser == null) {
                                      if (message.postUser?.id.toString() ==
                                          Prefobj.preferences?.get(Prefkeys.USER_ID)) {
                                        PersistentNavBarNavigator.pushNewScreen(context,
                                            screen: UserProfileScreen(), withNavBar: false);
                                        // NavigatorService.pushNamed(AppRoutes.userProfileScreen);
                                      } else {
                                        PersistentNavBarNavigator.pushNewScreen(context,
                                            screen:
                                                GetUserProfileById(userId: message.postUser?.id, stackonScreen: false),
                                            withNavBar: false);
                                      }
                                    } else {
                                      PersistentNavBarNavigator.pushNewScreen(context,
                                          screen:
                                              GetPostByIdScreen(postId: message.postId.toString(), isTextPost: true));
                                    }
                                  },
                                  child: Container(
                                      width: 250.0.w,
                                      padding: EdgeInsets.all(10.w),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).customColors.white,
                                        boxShadow: [
                                          BoxShadow(
                                            color: Theme.of(context).customColors.black.withOpacity(0.2),
                                            blurRadius: 2,
                                            offset: const Offset(0, 1),
                                          )
                                        ],
                                      ),
                                      child:
                                          _buildRichText(context, 6, message.title ?? "", message.description ?? "")),
                                )
                              : CustomImageView(
                                  onTap: () {
                                    if (message.postUser == null) {
                                      if (message.postUser?.id.toString() ==
                                          Prefobj.preferences?.get(Prefkeys.USER_ID)) {
                                        PersistentNavBarNavigator.pushNewScreen(context,
                                            screen: UserProfileScreen(), withNavBar: false);
                                        // NavigatorService.pushNamed(AppRoutes.userProfileScreen);
                                      } else {
                                        PersistentNavBarNavigator.pushNewScreen(context,
                                            screen:
                                                GetUserProfileById(userId: message.postUser?.id, stackonScreen: false),
                                            withNavBar: false);
                                      }
                                    } else {
                                      if (isVideo(message.file?.first ?? '')) {
                                        try {
                                          FocusManager.instance.primaryFocus?.unfocus();
                                          PersistentNavBarNavigator.pushNewScreen(
                                            context,
                                            screen: VideoReelPage(
                                              index: 0,
                                              reelService: ReelService(),
                                              screen: 'HomeFeed',
                                              postdata: PostData(
                                                  id: message.postId ?? 0,
                                                  title: message.title ?? '',
                                                  description: message.description ?? '',
                                                  location: '',
                                                  likes: 0,
                                                  dislikes: 0,
                                                  commentsCount: 0,
                                                  createdAt: message.createdAt ?? '',
                                                  files: message.file ?? [],
                                                  thumbnailFiles: [],
                                                  latestComment: '',
                                                  user: User(
                                                      userId: message.postUser?.id ?? 0,
                                                      username: message.postUser?.username ?? '',
                                                      name: message.postUser?.name ?? '',
                                                      profileImage:
                                                          "${APIConfig.mainbaseURL}${message.postUser?.profileImage}"),
                                                  isLiked: false,
                                                  isSaved: false,
                                                  width: 0,
                                                  height: 0,
                                                  isTextPost: false),
                                            ),
                                            withNavBar: false,
                                          );
                                        } catch (e) {
                                          Logger.lOG('Error $e');
                                        }
                                      } else {
                                        PersistentNavBarNavigator.pushNewScreen(
                                          context,
                                          screen: GetPostByIdScreen(postId: message.postId.toString()),
                                          withNavBar: false,
                                        );
                                      }
                                    }
                                  },
                                  imagePath: message.postMediaUrl != null
                                      ? "${APIConfig.mainbaseURL}/${message.postMediaUrl}"
                                      : Assets.images.pngs.flowkar.path,
                                  width: 250.0.w,
                                  radius: BorderRadius.vertical(bottom: Radius.circular(10.r)),
                                  placeholder: (p0, p1) {
                                    return Shimmer.fromColors(
                                      baseColor: Colors.grey.shade200,
                                      highlightColor: Colors.grey.shade50,
                                      child: Container(
                                        height: 250.h,
                                        width: 250.w,
                                        decoration: BoxDecoration(
                                          color: Colors.grey.shade200,
                                          borderRadius: BorderRadius.vertical(bottom: Radius.circular(10.r)),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                        ],
                      )
                : ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 280.w),
                    child: Container(
                      constraints: BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.75),
                      padding: EdgeInsets.only(
                        left: message.type == 'image' ? 0.w : 16.w,
                        top: message.type == 'image' ? 0.h : 10.h,
                        bottom: message.type == 'image' ? 0.h : 10.h,
                        right: (isCurrentUser ? 22.w : (message.type == 'image' ? 0.w : 16.w)),
                      ),
                      margin: EdgeInsets.fromLTRB(5.w, 0.h, 6.w, 2.h),
                      decoration: BoxDecoration(
                        color: isCurrentUser ? Theme.of(context).primaryColor : ThemeData().customColors.messagecolor,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(isCurrentUser ? 10.r : 0.r),
                          topRight: Radius.circular(10.r),
                          bottomRight: Radius.circular(isCurrentUser ? 0.r : 10.r),
                          bottomLeft: Radius.circular(10.r),
                        ),
                      ),
                      child: message.type == 'image'
                          ? InkWell(
                              onTap: () {
                                FocusScope.of(context).unfocus();
                                // PersistentNavBarNavigator.pushNewScreen(context,
                                //     screen: FlowkarImagePreview(
                                //       imagepath:
                                //           '${APIEndPoints.mainbaseURL}/${message.message}',
                                //     ));
                              },
                              child: CustomImageView(
                                imagePath: '${APIConfig.mainbaseURL}/${message.message}',
                                height: 250.0.h,
                                width: 150.0.w,
                                radius: BorderRadius.circular(4.0.r),
                                fit: BoxFit.cover,
                              ),
                            )
                          : message.type == 'custom'
                              ? SizedBox.shrink()
                              : message.message!.isUrl
                                  ? LinkPreview(
                                      url: message.message ?? '',
                                    )
                                  : Text(
                                      message.message ?? '',
                                      textAlign: TextAlign.start,
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          color: isCurrentUser
                                              ? ThemeData().customColors.white
                                              : ThemeData().customColors.black,
                                          fontSize: 16.0.sp,
                                          fontWeight: FontWeight.w500),
                                    ),
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildRichText(BuildContext context, int? maxLines, String title, String description) {
    String caption = title;
    if (description.isNotEmpty) {
      caption += '\n$description';
    }
    List<String> captionParts = caption.split('\n');

    String localTitle = '';
    String localDescription = '';

    if (captionParts.isNotEmpty) {
      localTitle = captionParts[0];
      if (captionParts.length > 1) {
        localDescription = captionParts.sublist(1).join('\n');
      }
    }

    List<TextSpan> spans = [];
    if (localTitle.isNotEmpty) {
      spans.add(
        TextSpan(
          text: localTitle,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontSize: 13.sp, fontWeight: FontWeight.bold),
        ),
      );
    }

    if (localDescription.isNotEmpty) {
      if (localTitle.isNotEmpty) {
        spans.add(
          TextSpan(
            text: '\n',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontSize: 12.sp, fontWeight: FontWeight.w500),
          ),
        );
      }

      spans.add(
        TextSpan(
          text: localDescription,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
        ),
      );
    }

    return RichText(
      text: TextSpan(children: spans),
      maxLines: maxLines,
      overflow: maxLines != null ? TextOverflow.ellipsis : TextOverflow.visible,
    );
  }

  Map<String, List<ChatMessageData>> _groupMessagesByDate(List<ChatMessageData> messages) {
    final Map<String, List<ChatMessageData>> groupedMessages = {};
    for (var message in messages) {
      final String apiTimeString = message.createdAt.toString().split("+").first;
      final formattedDate = getChatTimeAgoFromUTC(apiTimeString);
      groupedMessages.putIfAbsent(formattedDate, () => []).add(message);
    }
    return groupedMessages;
  }

  // Widget to show AI generated response
  Widget _buildAIResponseSection() {
    return BlocBuilder<SmChatBloc, SmChatState>(
      builder: (context, state) {
        return Container(
          margin: EdgeInsets.only(bottom: 10.0.h),
          padding: EdgeInsets.symmetric(horizontal: 12.0.w, vertical: 12.0.h),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Theme.of(context).primaryColor.withOpacity(0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.auto_fix_high_rounded, color: Theme.of(context).primaryColor, size: 16),
                  buildSizedBoxW(8),
                  Text(
                    'AI Generated Replay Massage',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).primaryColor,
                        ),
                  ),
                ],
              ),
              buildSizedBoxH(8),
              Text(
                state.aiGenerateMassageOrCommentModel?.result ?? "",
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              buildSizedBoxH(8),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () {
                      // Clear AI response
                      context.read<SmChatBloc>().add(ClearAIGeneratedMassageEvent());
                    },
                    child: Text(
                      'Dismiss',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).primaryColor,
                          ),
                    ),
                  ),
                  buildSizedBoxW(8),
                  ElevatedButton(
                    onPressed: () {
                      // Use AI response
                      // final rawAiResponse = state.aiGenerateMassageOrCommentModel?.result ?? "";
                      // final cleanedResponse = rawAiResponse.isNotEmpty ? json.decode(rawAiResponse) as String : "";
                      // if (state.isAIGeneratingMassageloading) {
                      //   _inputText.value = '';
                      // } else {
                      //   state.chatController?.text = cleanedResponse;
                      //   _inputText.value = cleanedResponse;
                      //   setState(() {});
                      // }

                      // // Clear AI response
                      // context.read<SmChatBloc>().add(ClearAIGeneratedMassageEvent());
                      final rawAiResponse = state.aiGenerateMassageOrCommentModel?.result ?? "";
                      String cleanedResponse = '';

                      if (rawAiResponse.isNotEmpty) {
                        try {
                          cleanedResponse = json.decode(rawAiResponse) as String;
                        } catch (e) {
                          // જો JSON decoding fail થાય, fallback to raw text
                          cleanedResponse = rawAiResponse;
                        }
                      }

                      if (state.isAIGeneratingMassageloading) {
                        _inputText.value = '';
                      } else {
                        state.chatController?.text = cleanedResponse;
                        _inputText.value = cleanedResponse;
                        setState(() {});
                      }

                      context.read<SmChatBloc>().add(ClearAIGeneratedMassageEvent());
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: Text('Apply'),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMessageTextField(SmChatState state, ThemeState themestate) {
    return ValueListenableBuilder<bool>(
      valueListenable: isRecording,
      builder: (_, isRecordingValue, child) {
        return Row(
          children: [
            Visibility(
              child: Expanded(
                child: SizedBox(
                  // color: Colors.red,
                  // height: 59,
                  child: Stack(
                    alignment: Alignment.bottomRight,
                    children: [
                      FlowkarTextFormField(
                        filled: true,
                        minLines: 1,
                        maxLines: 5,
                        focusNode: _chatFocusNode,
                        fillColor: Theme.of(context).customColors.white,
                        controller: state.chatController,
                        hintText: 'Enter Message',
                        context: context,
                        contentPadding: EdgeInsets.fromLTRB(12, 12, 90, 12),
                        onChanged: (inputText) {
                          context.read<SmChatBloc>().add(TypingSocketEvent(
                              userId: widget.isDeepLink == true ? widget.args.userId : widget.args['item'].userId,
                              isTyping: "1"));
                          _inputText.value = inputText;
                        },
                        /*
                        // suffixIcon: Row(
                        //   mainAxisSize: MainAxisSize.min,
                        //   children: [
                        //     // AI Button - Updated logic
                        //     BlocBuilder<SmChatBloc, SmChatState>(
                        //       builder: (context, state) {
                        //         return ValueListenableBuilder<String>(
                        //           valueListenable: _inputText,
                        //           builder: (_, inputTextValue, __) {
                        //             return BlocBuilder<SmChatBloc, SmChatState>(
                        //               builder: (context, currentState) {
                        //                 // Get last message from same user (not current user)
                        //                 ChatMessageData? lastSameUserMessage;
                        //                 if (currentState.chatMessageList.isNotEmpty) {
                        //                   final currentUserId =
                        //                       int.tryParse(Prefobj.preferences?.get(Prefkeys.USER_ID) ?? '0');
                        //                   lastSameUserMessage = currentState.chatMessageList
                        //                           .where((message) => message.sentBy != currentUserId)
                        //                           .isNotEmpty
                        //                       ? currentState.chatMessageList
                        //                           .where((message) => message.sentBy != currentUserId)
                        //                           .first
                        //                       : null;
                        //                 }

                        //                 // Check if last message is text type
                        //                 bool isLastMessageText = false;
                        //                 if (lastSameUserMessage != null) {
                        //                   isLastMessageText = lastSameUserMessage.type == 'text';
                        //                 }

                        //                 // AI button should be enabled when:
                        //                 // 1. Last message exists and is text type, OR
                        //                 // 2. Last message doesn't exist or is not text type BUT text field has content
                        //                 bool isAIButtonEnabled = (lastSameUserMessage != null && isLastMessageText) ||
                        //                     ((!isLastMessageText || lastSameUserMessage == null) &&
                        //                             inputTextValue.trim().isNotEmpty ||
                        //                         _inputText.value.trim().isNotEmpty);

                        //                 return InkWell(
                        //                   onTap: !isAIButtonEnabled
                        //                       ? null
                        //                       : () {
                        //                           String messageToPass = '';

                        //                           // If last message exists and is text type, pass that message
                        //                           if (lastSameUserMessage != null && isLastMessageText) {
                        //                             messageToPass = lastSameUserMessage.message ?? '';
                        //                             Logger.lOG('Passing last same user text message: $messageToPass');
                        //                           }
                        //                           // If last message doesn't exist or is not text type, pass text field content
                        //                           else if ((!isLastMessageText || lastSameUserMessage == null) &&
                        //                               inputTextValue.trim().isNotEmpty) {
                        //                             messageToPass = inputTextValue.trim();
                        //                             Logger.lOG('Passing text field content: $messageToPass');
                        //                           }

                        //                           // Call AI generate message event with the determined message
                        //                           if (messageToPass.isNotEmpty) {
                        //                             context.read<SmChatBloc>().add(AIgenerateMassageEvent(
                        //                                 context: context, massageText: messageToPass));
                        //                           }
                        //                         },
                        //                   child: CircleAvatar(
                        //                     radius: 15.r,
                        //                     backgroundColor: !isAIButtonEnabled
                        //                         ? Theme.of(context).primaryColor.withOpacity(0.5)
                        //                         : Theme.of(context).primaryColor,
                        //                     child: currentState.isAIGeneratingMassageloading
                        //                         ? CupertinoActivityIndicator(
                        //                             color: Colors.white,
                        //                           )
                        //                         : Icon(Icons.auto_fix_high_rounded,
                        //                             color: !isAIButtonEnabled
                        //                                 ? Colors.white.withOpacity(0.5)
                        //                                 : Colors.white,
                        //                             size: 20),
                        //                   ),
                        //                 );
                        //               },
                        //             );
                        //           },
                        //         );
                        //       },
                        //     ),

                        //     BlocBuilder<SmChatBloc, SmChatState>(
                        //       builder: (context, state) {
                        //         if (state.isAIGeneratingMassageloading) {
                        //           _inputText.value = '';
                        //         } else {
                        //           _inputText.value = state.chatController?.text.trim() ?? '';
                        //         }
                        //         return ValueListenableBuilder<String>(
                        //           valueListenable: _inputText,
                        //           builder: (_, inputTextValue, __) {
                        //             if (inputTextValue.isNotEmpty) {
                        //               return Padding(
                        //                 padding: EdgeInsets.only(right: 0.0.w),
                        //                 child: InkWell(
                        //                   onTap: () {
                        //                     final messageText = state.chatController?.text.trim() ?? '';
                        //                     if (messageText.isNotEmpty) {
                        //                       context.read<SmChatBloc>().add(SendMessageEvent(
                        //                             message: messageText,
                        //                             file: '',
                        //                             touserId: int.tryParse((widget.isDeepLink == true
                        //                                         ? widget.args.userId
                        //                                         : widget.args['item']?.userId)
                        //                                     .toString()) ??
                        //                                 0,
                        //                             type: 'text',
                        //                           ));
                        //                       context.read<SmChatBloc>().add(ClearAIGeneratedMassageEvent());
                        //                       setState(() {});
                        //                       state.chatController?.clear();
                        //                       _inputText.value = '';
                        //                       // FocusScope.of(context).requestFocus(FocusNode());
                        //                     }
                        //                   },
                        //                   child: CustomImageView(
                        //                     imagePath: Assets.images.icons.other.icSend.path,
                        //                     margin: EdgeInsets.all(12),
                        //                     height: 22.0.h,
                        //                   ),
                        //                 ),
                        //               );
                        //             } else {
                        //               context.read<SmChatBloc>().add(TypingSocketEvent(
                        //                     userId: widget.isDeepLink == true
                        //                         ? widget.args.userId
                        //                         : widget.args['item'].userId,
                        //                     isTyping: "0",
                        //                   ));
                        //               return SizedBox.shrink();
                        //             }
                        //           },
                        //         );
                        //       },
                        //     ),
                        //   ],
                        // ),*/
                      ),
                      Positioned(
                        bottom: 08.h,
                        right: 10.w,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // AI Button - Updated logic
                            BlocBuilder<SmChatBloc, SmChatState>(
                              builder: (context, state) {
                                return ValueListenableBuilder<String>(
                                  valueListenable: _inputText,
                                  builder: (_, inputTextValue, __) {
                                    return BlocBuilder<SmChatBloc, SmChatState>(
                                      builder: (context, currentState) {
                                        // Get last message from same user (not current user)
                                        ChatMessageData? lastSameUserMessage;
                                        if (currentState.chatMessageList.isNotEmpty) {
                                          final currentUserId =
                                              int.tryParse(Prefobj.preferences?.get(Prefkeys.USER_ID) ?? '0');
                                          lastSameUserMessage = currentState.chatMessageList
                                                  .where((message) => message.sentBy != currentUserId)
                                                  .isNotEmpty
                                              ? currentState.chatMessageList
                                                  .where((message) => message.sentBy != currentUserId)
                                                  .first
                                              : null;
                                        }

                                        // Check if last message is text type
                                        bool isLastMessageText = false;
                                        if (lastSameUserMessage != null) {
                                          isLastMessageText = lastSameUserMessage.type == 'text';
                                        }

                                        // AI button should be enabled when:
                                        // 1. Last message exists and is text type, OR
                                        // 2. Last message doesn't exist or is not text type BUT text field has content
                                        bool isAIButtonEnabled = (lastSameUserMessage != null && isLastMessageText) ||
                                            ((!isLastMessageText || lastSameUserMessage == null) &&
                                                    inputTextValue.trim().isNotEmpty ||
                                                _inputText.value.trim().isNotEmpty);

                                        return InkWell(
                                          onTap: !isAIButtonEnabled
                                              ? null
                                              : () {
                                                  String messageToPass = '';

                                                  // If last message exists and is text type, pass that message
                                                  if (lastSameUserMessage != null && isLastMessageText) {
                                                    messageToPass = lastSameUserMessage.message ?? '';
                                                    Logger.lOG('Passing last same user text message: $messageToPass');
                                                  }
                                                  // If last message doesn't exist or is not text type, pass text field content
                                                  else if ((!isLastMessageText || lastSameUserMessage == null) &&
                                                      inputTextValue.trim().isNotEmpty) {
                                                    messageToPass = inputTextValue.trim();
                                                    Logger.lOG('Passing text field content: $messageToPass');
                                                  }

                                                  // Call AI generate message event with the determined message
                                                  if (messageToPass.isNotEmpty) {
                                                    context.read<SmChatBloc>().add(AIgenerateMassageEvent(
                                                        context: context, massageText: messageToPass));
                                                  }
                                                },
                                          child: CircleAvatar(
                                            radius: 15.r,
                                            backgroundColor: !isAIButtonEnabled
                                                ? Theme.of(context).primaryColor.withOpacity(0.5)
                                                : Theme.of(context).primaryColor,
                                            child: currentState.isAIGeneratingMassageloading
                                                ? CupertinoActivityIndicator(
                                                    color: Colors.white,
                                                  )
                                                : Icon(Icons.auto_fix_high_rounded,
                                                    color: !isAIButtonEnabled
                                                        ? Colors.white.withOpacity(0.5)
                                                        : Colors.white,
                                                    size: 20),
                                          ),
                                        );
                                      },
                                    );
                                  },
                                );
                              },
                            ),
                            buildSizedBoxW(10),

                            BlocBuilder<SmChatBloc, SmChatState>(
                              builder: (context, state) {
                                if (state.isAIGeneratingMassageloading) {
                                  _inputText.value = '';
                                } else {
                                  _inputText.value = state.chatController?.text.trim() ?? '';
                                }
                                return ValueListenableBuilder<String>(
                                  valueListenable: _inputText,
                                  builder: (_, inputTextValue, __) {
                                    if (inputTextValue.isNotEmpty) {
                                      return InkWell(
                                        onTap: () {
                                          final messageText = state.chatController?.text.trim() ?? '';
                                          if (messageText.isNotEmpty) {
                                            context.read<SmChatBloc>().add(SendMessageEvent(
                                                  message: messageText,
                                                  file: '',
                                                  touserId: int.tryParse((widget.isDeepLink == true
                                                              ? widget.args.userId
                                                              : widget.args['item']?.userId)
                                                          .toString()) ??
                                                      0,
                                                  type: 'text',
                                                ));
                                            context.read<SmChatBloc>().add(ClearAIGeneratedMassageEvent());
                                            setState(() {});
                                            state.chatController?.clear();
                                            _inputText.value = '';
                                            // FocusScope.of(context).requestFocus(FocusNode());
                                          }
                                        },
                                        child: CustomImageView(
                                          imagePath: Assets.images.icons.other.icSend.path,
                                          margin: EdgeInsets.only(right: 8.0.w),
                                          height: 22.0.h,
                                        ),
                                      );
                                    } else {
                                      context.read<SmChatBloc>().add(TypingSocketEvent(
                                            userId: widget.isDeepLink == true
                                                ? widget.args.userId
                                                : widget.args['item'].userId,
                                            isTyping: "0",
                                          ));
                                      return SizedBox.shrink();
                                    }
                                  },
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // FutureOr<void> _cancelRecording() async {
  //   assert(
  //     defaultTargetPlatform == TargetPlatform.iOS ||
  //         defaultTargetPlatform == TargetPlatform.android,
  //     "Voice messages are only supported with android and ios platform",
  //   );
  //   if (!isRecording.value) return;
  //   // final path = await controller?.stop();
  //   if (path == null) {
  //     isRecording.value = false;
  //     return;
  //   }
  //   final file = File(path);

  //   if (await file.exists()) {
  //     await file.delete();
  //   }

  //   isRecording.value = false;
  // }

  // Future<void> _recordOrStop() async {
  //   assert(
  //     defaultTargetPlatform == TargetPlatform.iOS ||
  //         defaultTargetPlatform == TargetPlatform.android,
  //     "Voice messages are only supported with android and ios platform",
  //   );
  //   if (!isRecording.value) {
  //     await controller?.record(
  //       androidEncoder: AndroidEncoder.aac,
  //       iosEncoder: IosEncoder.kAudioFormatMPEG4AAC,
  //       androidOutputFormat: AndroidOutputFormat.mpeg4,
  //       bitRate: 128000,
  //       sampleRate: 44100,
  //     );
  //     isRecording.value = true;
  //   } else {
  //     final path = await controller?.stop();
  //     isRecording.value = false;
  //     path;
  //     Logger.lOG(path);
  //     // Check if an image is selected
  //     if (path != null && path.isNotEmpty) {
  //       // Get the MIME type of the image file
  //       String? mimeType = lookupMimeType(path);

  //       if (mimeType != null) {
  //         // Convert the image file to Base64
  //         File audioFile = File(path);
  //         List<int> audioBytes = await audioFile.readAsBytes();
  //         String base64audio = base64Encode(audioBytes);

  //         // Create the Data URI string
  //         String dataUri = 'data:audio/m4a;;base64,$base64audio';

  //         // Send the Data URI as a chat message

  //         context.read<SmChatBloc>().add(SendMessageEvent(
  //               file: dataUri,
  //               message: "",
  //               touserId: int.tryParse(widget.args['item'].userId.toString()) ?? 0,
  //               type: 'custom', // or any other type indicating an image
  //             ));
  //       } else {
  //         Logger.lOG('Could not determine MIME type.');
  //       }
  //     }
  //   }
  // }

  void onIconPressed(ImageSource imageSource) async {
    try {
      // Pick an image from the specified image source
      final XFile? image = await _imagePicker.pickImage(
        source: imageSource,
        preferredCameraDevice: CameraDevice.rear,
      );
      if (image != null) {
        // NavigatorService.pushNamed(AppRoutes.chatImagePreview,
        //     arguments: [image, widget.args['item'].userId.toString()]);
      }
    } catch (e) {
      Logger.lOG('Error picking/sending image: ${e.toString()}');
    }
  }
}

String getChatTimeAgoFromUTC(String utcString) {
  try {
    String cleanUtcString = utcString;
    if (!utcString.endsWith('Z') && !utcString.contains('+')) {
      cleanUtcString = "${utcString}Z";
    }

    final DateTime utcDateTime = DateTime.parse(cleanUtcString);
    final DateTime localDateTime = utcDateTime.toLocal();
    final DateTime now = DateTime.now();

    // Today
    if (_isSameDay(localDateTime, now)) {
      final String formatted = DateFormat('hh:mm a').format(localDateTime);
      return 'Today at $formatted';
    }

    // Yesterday
    if (_isSameDay(localDateTime, now.subtract(const Duration(days: 1)))) {
      final String formatted = DateFormat('hh:mm a').format(localDateTime);
      return 'Yesterday at $formatted';
    }

    // Last week
    if (now.difference(localDateTime).inDays < 7) {
      final String formatted = DateFormat('EEEE hh:mm a').format(localDateTime);
      return formatted;
    }

    if (now.difference(localDateTime).inDays > 364) {
      final String formatted = DateFormat('dd MMM, yyyy').format(localDateTime);
      return formatted;
    }

    final String formatted = DateFormat('dd MMM, hh:mm a').format(localDateTime);
    return formatted;
  } catch (e) {
    return "Invalid time";
  }
}

bool _isSameDay(DateTime date1, DateTime date2) {
  return date1.year == date2.year && date1.month == date2.month && date1.day == date2.day;
}
