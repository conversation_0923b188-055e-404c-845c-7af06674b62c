FacebookImpretionAndFollowerModel deserializeFacebookImpretionAndFollowerModel(Map<String, dynamic> json) =>
    FacebookImpretionAndFollowerModel.fromJson(json);

class FacebookImpretionAndFollowerModel {
  final bool status;
  final String message;
  final FacebookImpretionFollowerData data;

  FacebookImpretionAndFollowerModel({
    required this.status,
    required this.message,
    required this.data,
  });

  factory FacebookImpretionAndFollowerModel.fromJson(Map<String, dynamic> json) {
    return FacebookImpretionAndFollowerModel(
      status: json['status'],
      message: json['message'],
      data: FacebookImpretionFollowerData.fromJson(json['data']),
    );
  }

  Map<String, dynamic> toJson() => {
        'status': status,
        'message': message,
        'data': data.toJson(),
      };

  FacebookImpretionAndFollowerModel copyWith({
    bool? status,
    String? message,
    FacebookImpretionFollowerData? data,
  }) {
    return FacebookImpretionAndFollowerModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

class FacebookImpretionFollowerData {
  final FacebookMetrics metrics;
  final FacebookFollowerGraphData graphData;

  FacebookImpretionFollowerData({
    required this.metrics,
    required this.graphData,
  });

  factory FacebookImpretionFollowerData.fromJson(Map<String, dynamic> json) {
    return FacebookImpretionFollowerData(
      metrics: FacebookMetrics.fromJson(json['metrics']),
      graphData: FacebookFollowerGraphData.fromJson(json['graph_data']),
    );
  }

  Map<String, dynamic> toJson() => {
        'metrics': metrics.toJson(),
        'graph_data': graphData.toJson(),
      };

  FacebookImpretionFollowerData copyWith({
    FacebookMetrics? metrics,
    FacebookFollowerGraphData? graphData,
  }) {
    return FacebookImpretionFollowerData(
      metrics: metrics ?? this.metrics,
      graphData: graphData ?? this.graphData,
    );
  }
}

class FacebookMetrics {
  final int likes;
  final double dailyLikes;
  final double likesPerPost;
  final double dailyPageView;
  final double dailyPosts;
  final double postsPerWeek;

  FacebookMetrics({
    required this.likes,
    required this.dailyLikes,
    required this.likesPerPost,
    required this.dailyPageView,
    required this.dailyPosts,
    required this.postsPerWeek,
  });

  factory FacebookMetrics.fromJson(Map<String, dynamic> json) {
    return FacebookMetrics(
      likes: json['likes'],
      dailyLikes: (json['daily_likes'] ?? 0).toDouble(),
      likesPerPost: (json['likes_per_post'] ?? 0).toDouble(),
      dailyPageView: (json['daily_page_view'] ?? 0).toDouble(),
      dailyPosts: (json['daily_posts'] ?? 0).toDouble(),
      postsPerWeek: (json['posts_per_week'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() => {
        'likes': likes,
        'daily_likes': dailyLikes,
        'likes_per_post': likesPerPost,
        'daily_page_view': dailyPageView,
        'daily_posts': dailyPosts,
        'posts_per_week': postsPerWeek,
      };

  FacebookMetrics copyWith({
    int? likes,
    double? dailyLikes,
    double? likesPerPost,
    double? dailyPageView,
    double? dailyPosts,
    double? postsPerWeek,
  }) {
    return FacebookMetrics(
      likes: likes ?? this.likes,
      dailyLikes: dailyLikes ?? this.dailyLikes,
      likesPerPost: likesPerPost ?? this.likesPerPost,
      dailyPageView: dailyPageView ?? this.dailyPageView,
      dailyPosts: dailyPosts ?? this.dailyPosts,
      postsPerWeek: postsPerWeek ?? this.postsPerWeek,
    );
  }
}

class FacebookFollowerGraphData {
  final int totalFollows;
  final int totalFans;
  final int totalImpressions;
  final int totalImpressionsUnique;
  final int totalViewsTotal;
  final List<FacebookFollowerGraphEntry> data;

  FacebookFollowerGraphData({
    required this.totalFollows,
    required this.totalFans,
    required this.totalImpressions,
    required this.totalImpressionsUnique,
    required this.totalViewsTotal,
    required this.data,
  });

  factory FacebookFollowerGraphData.fromJson(Map<String, dynamic> json) {
    return FacebookFollowerGraphData(
      totalFollows: json['total_follows'],
      totalFans: json['total_fans'],
      totalImpressions: json['total_impressions'],
      totalImpressionsUnique: json['total_impressions_unique'],
      totalViewsTotal: json['total_views_total'],
      data: List<FacebookFollowerGraphEntry>.from(
        json['data'].map((x) => FacebookFollowerGraphEntry.fromJson(x)),
      ),
    );
  }

  Map<String, dynamic> toJson() => {
        'total_follows': totalFollows,
        'total_fans': totalFans,
        'total_impressions': totalImpressions,
        'total_impressions_unique': totalImpressionsUnique,
        'total_views_total': totalViewsTotal,
        'data': data.map((x) => x.toJson()).toList(),
      };

  FacebookFollowerGraphData copyWith({
    int? totalFollows,
    int? totalFans,
    int? totalImpressions,
    int? totalImpressionsUnique,
    int? totalViewsTotal,
    List<FacebookFollowerGraphEntry>? data,
  }) {
    return FacebookFollowerGraphData(
      totalFollows: totalFollows ?? this.totalFollows,
      totalFans: totalFans ?? this.totalFans,
      totalImpressions: totalImpressions ?? this.totalImpressions,
      totalImpressionsUnique: totalImpressionsUnique ?? this.totalImpressionsUnique,
      totalViewsTotal: totalViewsTotal ?? this.totalViewsTotal,
      data: data ?? this.data,
    );
  }
}

class FacebookFollowerGraphEntry {
  final String date;
  final int follows;
  final int fans;
  final int impressions;
  final int impressionsUnique;
  final int viewsTotal;

  FacebookFollowerGraphEntry({
    required this.date,
    required this.follows,
    required this.fans,
    required this.impressions,
    required this.impressionsUnique,
    required this.viewsTotal,
  });

  factory FacebookFollowerGraphEntry.fromJson(Map<String, dynamic> json) {
    return FacebookFollowerGraphEntry(
      date: json['date'],
      follows: json['follows'],
      fans: json['fans'],
      impressions: json['impressions'],
      impressionsUnique: json['impressions_unique'],
      viewsTotal: json['views_total'],
    );
  }

  Map<String, dynamic> toJson() => {
        'date': date,
        'follows': follows,
        'fans': fans,
        'impressions': impressions,
        'impressions_unique': impressionsUnique,
        'views_total': viewsTotal,
      };

  FacebookFollowerGraphEntry copyWith({
    String? date,
    int? follows,
    int? fans,
    int? impressions,
    int? impressionsUnique,
    int? viewsTotal,
  }) {
    return FacebookFollowerGraphEntry(
      date: date ?? this.date,
      follows: follows ?? this.follows,
      fans: fans ?? this.fans,
      impressions: impressions ?? this.impressions,
      impressionsUnique: impressionsUnique ?? this.impressionsUnique,
      viewsTotal: viewsTotal ?? this.viewsTotal,
    );
  }
}
