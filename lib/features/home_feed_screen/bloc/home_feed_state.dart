part of 'home_feed_bloc.dart';

class HomeFeedState extends Equatable {
  final List<PostData> posts;
  final bool allFetch;
  final int postId;
  final bool isLoadingMore;
  final bool homeFeedLoading;
  final PostResponseModel? postResponsemodel;
  final int postPage;
  final List<VideoData> video;
  final bool allVideoFetch;
  final bool isVideoLoadingMore;
  final bool homeFeedVideoLoading;
  final VideoResponseModel? videoResponseModel;
  final int videoPage;
  //MARK: Comment
  final GetCommentModel? getcommentModel;
  final bool isCommentloding;
  final TextEditingController? commentTextController;
  final TextEditingController? replycommentTextController;
  //MARK:Delete & Save Post
  final bool isDeleteLoading;
  // Save Post
  final List<SavePostData> savedposts;
  final bool isSavePostLoadingMore;
  final bool isSavePostloding;
  final bool savePosthasMore;
  final SavePostModel? savepostModel;
  final int savepospage;
  final bool isSavePostLiked;
  final bool savePostallFetch;
  //MARK:Discover
  final bool isdiscoverloding;
  final bool isDiscoverLoadingMore;
  final bool isDiscoverhasMore;
  final int isDiscoverpage;
  final bool isDiscoverallFetch;
  final List<PostData> isDiscoverposts;
  final PostResponseModel? getAllPostModel;
  //MARK: Analytics
  final bool anyliticsLoading;
  final AnalyticsModel? analyticsModel;
  // MARK:get User Profile by Id
  final bool profileByIdLoading;
  final UserProfileDetailModel? userProfile;
  final PostResponseModel? getAllUserPostbyidModel;
  final List<PostData> isuserProfileposts;
  final int userpostbyIdPage;
  final bool profilPosteByIdLoading;
  final bool profilPosteByIdLoadingmore;
  final bool profilPosteByIdallFetch;

  // MARK:Get User Profile and post
  final bool getProfileLoading;
  // final UserProfileDetailModel? getProfileModel;
  final PostResponseModel? getProfilePostmodel;
  final List<PostData> getProfilePost;
  final int getProfilepostpage;
  final bool getprofilrPosteLoading;
  final bool getprofilPosteLoadingmore;
  final bool getprofilPostedallFetch;
  // get User text Post
  final bool getuserTextPostLoading;
  final GetUserTextPostModel? getUserTextPostModel;
  final List<UserTextPostData> getUserTextPostData;
  final int getUserTextPostPage;
  final bool getUserTextPostLoadingMore;
  final bool getUserTextPostAllFetch;
//MARK:Get User Profile video
  final List<VideoData> getProfilevideo;
  final bool getProfileallVideoFetch;
  final bool getProfileisVideoLoadingMore;
  final bool getProfileVideoLoading;
  final VideoResponseModel? getProfilevideoResponseModel;
  final int getProfilevideoPage;
  //MARK: get User Video by Id
  final List<VideoData> getProfileByIDvideo;
  final bool getProfileallByIDVideoFetch;
  final bool getProfileisVideoByIDLoadingMore;
  final bool getProfileVideoByIDLoading;
  final VideoResponseModel? getProfilevideoByIDResponseModel;
  final int getProfilevideoByIDPage;
  //MARK:Get User Text Post by Id
  final List<UserTextPostData> getUserByIdTextPostData;
  final bool getUserByIdTextPostLoadingMore;
  final bool getUserByIdTextPostAllFetch;
  final int getUserByIdTextPostPage;
  final bool getUserByIdTextPostLoading;

  //MARK: Edit Post
  // Get PostState
  final UserProfileModel? userProfileModel;
  final bool isGetPostLoading;
  final String? getPostError;

  // MARK:Story
  // final GetAllStoryModel? getallstorymodel;
  // final GetSinglStoryModel? getsinglestorymodel;
  // final List<StoryData> storyData;
  // final int storyPage;
  final bool storyisloding;
  // final List<SingleStoryData> singleStoryData;
  final NewStoryModel? newStoryModel;
  final List<NewStory> newStoryData;
  final List<Stories> newstory;
  final UploadStoryModel? uploadStoryModel;
  final int tagpage;
  final TagPostmodel? tagPostmodel;
  final List<TagPostData> tagPostData;
  final bool istagLoadingMore;
  final bool isloding;

// MARK:Follow
  final List<FolllowData> postLikeList;
  final bool isBlockLoading;
  final BlockUserModel? blockUserModel;
  final List<BlockedUsersData> blockedUsersList;
  final TextEditingController? searchController;

// MARK:hash tag post
  final HashtagPostModel? hashtagPostModel;
  final List<HashtagPostData> hashtagPostData;

// MARK:Draft Post
  final bool isGetDraftPostLoading;
  final bool isGetDraftPostLoadingMore;
  final int draftPostPage;
  final DraftPostModel? draftPostModel;
  final List<DraftPostData> draftPosts;
  final bool isdeleteDraftPostLoading;
  final bool isUploadDraftPostLoading;

  //MARK:Post Share
  final PostShareModel? postShareModel;
  final SharePostMessageModel? sharePostMessageModel;
  final bool isPostShareLoading;
  final String searchQuery;
  final List<Map<String, dynamic>> searchHistory;
  final List<SearchUserData>? searchuserList;

  //MARK:Notification Post
  final List<GetNotificationPostData> getpostbyIdData;
  final bool fetchPostLoading;
  final GetPostbyIdModel? getPostbyIdModel;

  //MARK:Deep Link Post
  final bool isDeepLinkPostLoading;
  final DeepLinkPostModel? deepLinkPostModel;
  final String? errorMessage;

  //MARK:Reels
  final List<ReelData> reels;
  final bool isReelsLoading;
  final bool isReelsLoadingMore;
  final bool isReelsAllFetch;
  final int reelsPage;
  final ReelData? reelsModel;

  //Live user
  final LiveUserModel? liveUserModel;
  final bool liveUserisloding;
  final List<LiveNewStory> livenewStoryData;

  // MARK:Planner Schedule Post web
  final bool isSchedulePostWebLoading;
  final List<ScheduledPostWebData> schedulePostWeb;
  final ScheduledPostsWebModel? schedulePostWebModel;
  final bool isPlannerPostListLoading;
  final bool isPlannerPostListLoadingMore;
  final bool isPlannerPostListAllFetch;
  final int plannerPostListPage;
  final PlannerListPostModel? plannerListPostModel;
  final List<PlannerPostData> plannerPostData;
  final bool isAIGeneratingMassageloading;
  final AiGenerateMassageOrComment? aiGenerateMassageOrCommentModel;

  const HomeFeedState({
    this.postId = -1,
    this.posts = const [],
    this.allFetch = false,
    this.isLoadingMore = false,
    this.homeFeedLoading = false,
    this.postResponsemodel,
    this.postPage = 1,
    this.getcommentModel,
    this.isCommentloding = false,
    this.commentTextController,
    this.replycommentTextController,
    this.video = const [],
    this.allVideoFetch = false,
    this.isVideoLoadingMore = false,
    this.homeFeedVideoLoading = false,
    this.videoResponseModel,
    this.videoPage = 1,
    this.isdiscoverloding = false,
    this.isDiscoverLoadingMore = false,
    this.isDiscoverhasMore = false,
    this.isDiscoverpage = 1,
    this.isDiscoverallFetch = false,
    this.isDiscoverposts = const [],
    this.getAllPostModel,
    this.isDeleteLoading = false,
    // SavePost

    this.savedposts = const [],
    this.isSavePostLoadingMore = false,
    this.isSavePostloding = false,
    this.savePosthasMore = false,
    this.savepostModel,
    this.savepospage = 1,
    this.isSavePostLiked = false,
    this.savePostallFetch = false,

    // Alalytics
    this.anyliticsLoading = false,
    this.analyticsModel,
    // get User Profile by Id
    this.profileByIdLoading = false,
    this.userProfile,
    this.isuserProfileposts = const [],
    this.userpostbyIdPage = 1,
    this.profilPosteByIdLoading = false,
    this.profilPosteByIdLoadingmore = false,
    this.profilPosteByIdallFetch = false,
    this.getAllUserPostbyidModel,
    // Get User Text Post by Id
    this.getUserByIdTextPostData = const [],
    this.getUserByIdTextPostLoadingMore = false,
    this.getUserByIdTextPostAllFetch = false,
    this.getUserByIdTextPostPage = 1,
    this.getUserByIdTextPostLoading = false,
    // this.getprofilePosteLoading = false,
    // Get User Profile
    // Get User Profile
    this.getProfileLoading = false,
    // this.getProfileModel,
    this.getProfilePostmodel,
    this.getProfilePost = const [],
    this.getProfilepostpage = 1,
    this.getprofilrPosteLoading = false,
    this.getprofilPosteLoadingmore = false,
    this.getprofilPostedallFetch = false,
    // get User text Post
    this.getuserTextPostLoading = false,
    this.getUserTextPostModel,
    this.getUserTextPostData = const [],
    this.getUserTextPostPage = 1,
    this.getUserTextPostLoadingMore = false,
    this.getUserTextPostAllFetch = false,
//Get User Profile video
    this.getProfilevideo = const [],
    this.getProfileallVideoFetch = false,
    this.getProfileisVideoLoadingMore = false,
    this.getProfileVideoLoading = false,
    this.getProfilevideoResponseModel,
    this.getProfilevideoPage = 1,
//Get User Profile video
    this.getProfileByIDvideo = const [],
    this.getProfileallByIDVideoFetch = false,
    this.getProfileisVideoByIDLoadingMore = false,
    this.getProfileVideoByIDLoading = false,
    this.getProfilevideoByIDResponseModel,
    this.getProfilevideoByIDPage = 1,
    // Get Post state
    this.userProfileModel,
    this.isGetPostLoading = false,
    this.getPostError,
    this.searchQuery = '',
    this.searchHistory = const [],
    this.searchuserList,
    this.isBlockLoading = false,

    // Story
    // this.getallstorymodel,
    // this.getsinglestorymodel,
    // this.storyData = const [],
    // this.storyPage = 1,
    this.storyisloding = false,
    // this.singleStoryData = const [],
    this.newStoryModel,
    this.newStoryData = const [],
    this.newstory = const [],
    this.uploadStoryModel,
    this.tagpage = 1,
    this.tagPostmodel,
    this.tagPostData = const [],
    this.istagLoadingMore = false,
    this.isloding = false,
    this.postLikeList = const [],
    this.blockUserModel,
    this.blockedUsersList = const [],
    this.searchController,
    // hash tag post
    this.hashtagPostModel,
    this.hashtagPostData = const [],
    // Get Draft Post
    this.isGetDraftPostLoading = false,
    this.draftPostPage = 1,
    this.draftPosts = const [],
    this.draftPostModel,
    this.isdeleteDraftPostLoading = false,
    this.isUploadDraftPostLoading = false,
    this.isGetDraftPostLoadingMore = false,

    //Post Share
    this.postShareModel,
    this.sharePostMessageModel,
    this.isPostShareLoading = false,

    //Notification Post
    this.getpostbyIdData = const [],
    this.fetchPostLoading = false,
    this.getPostbyIdModel,

    //Deep Link Post
    this.isDeepLinkPostLoading = false,
    this.deepLinkPostModel,
    this.errorMessage,

    //Reels
    this.reels = const [],
    this.isReelsLoading = false,
    this.isReelsLoadingMore = false,
    this.isReelsAllFetch = false,
    this.reelsPage = 1,
    this.reelsModel,
    this.liveUserModel,
    this.liveUserisloding = false,
    this.livenewStoryData = const [],

    //Planner schedule post web
    this.isSchedulePostWebLoading = false,
    this.schedulePostWebModel,
    this.schedulePostWeb = const [],
    this.isPlannerPostListLoading = false,
    this.isPlannerPostListAllFetch = false,
    this.plannerPostListPage = 1,
    this.isPlannerPostListLoadingMore = false,
    this.plannerPostData = const [],
    this.plannerListPostModel,
    this.isAIGeneratingMassageloading = false,
    this.aiGenerateMassageOrCommentModel,
  });

  @override
  List<Object?> get props => [
        posts,
        postId,
        allFetch,
        isLoadingMore,
        homeFeedLoading,
        postResponsemodel,
        postPage,
        getcommentModel,
        isCommentloding,
        commentTextController,
        replycommentTextController,
        video,
        allVideoFetch,
        isVideoLoadingMore,
        homeFeedVideoLoading,
        videoResponseModel,
        videoPage,
        isDeleteLoading,
        // SavePost
        savedposts,
        isSavePostLoadingMore,
        isSavePostloding,
        savePosthasMore,
        savepostModel,
        savepospage,
        isSavePostLiked,
        savePostallFetch,

        isdiscoverloding,
        isDiscoverLoadingMore,
        isDiscoverhasMore,
        isDiscoverpage,
        isDiscoverallFetch,
        isDiscoverposts,
        getAllPostModel, isDeleteLoading,
        getAllPostModel,
        // Analytics
        anyliticsLoading,
        analyticsModel,
        // get User Profile by Id
        profileByIdLoading,
        userProfile,
        isuserProfileposts,
        userpostbyIdPage,
        profilPosteByIdLoading,
        profilPosteByIdLoadingmore,
        profilPosteByIdallFetch,
        getAllUserPostbyidModel,
        // Get User Text Post by Id
        getUserByIdTextPostData,
        getUserByIdTextPostLoadingMore,
        getUserByIdTextPostAllFetch,
        getUserByIdTextPostPage,
        getUserByIdTextPostLoading,
        //Get User Profile

        getProfileLoading,
        // getProfileModel,
        getProfilePostmodel,
        getProfilePost,
        getProfilepostpage,
        getprofilrPosteLoading,
        getprofilPosteLoadingmore,
        getprofilPostedallFetch,
        // get User text Post
        getuserTextPostLoading,
        getUserTextPostModel,
        getUserTextPostData,
        getUserTextPostPage,
        getUserTextPostLoadingMore,
        getUserTextPostAllFetch,

        //
        getProfilevideo,
        getProfileallVideoFetch,
        getProfileisVideoLoadingMore,
        getProfileVideoLoading,
        getProfilevideoResponseModel,
        getProfilevideoPage,
        //
        getProfileByIDvideo,
        getProfileallByIDVideoFetch,
        getProfileisVideoByIDLoadingMore,
        getProfileVideoByIDLoading,
        getProfilevideoByIDResponseModel,
        getProfilevideoByIDPage,
        // Get PostState
        userProfileModel,
        isGetPostLoading,
        getPostError,

        // Story
        // getallstorymodel,
        // getsinglestorymodel,
        // storyData,
        // storyPage,
        storyisloding,
        // singleStoryData,
        newStoryModel,
        newStoryData,
        newstory,
        uploadStoryModel,
        istagLoadingMore,
        isloding,
        tagPostData,
        tagPostmodel,
        tagpage,
        postLikeList,
        blockUserModel,
        isBlockLoading,
        blockedUsersList,
        searchController,
        hashtagPostData,
        hashtagPostModel,
        // Get Draft Post
        isGetDraftPostLoading,
        draftPostPage,
        draftPosts,
        isdeleteDraftPostLoading,
        isUploadDraftPostLoading,
        draftPostModel,
        isGetDraftPostLoadingMore,
        //
        postShareModel,
        sharePostMessageModel,
        isPostShareLoading,
        searchQuery,
        searchHistory,
        searchuserList,

        //Notification Post
        getpostbyIdData,
        fetchPostLoading,
        getPostbyIdModel,

        //Deep Link Post
        isDeepLinkPostLoading,
        deepLinkPostModel,
        errorMessage,

        //Reels
        reels,
        isReelsLoading,
        isReelsLoadingMore,
        isReelsAllFetch,
        reelsPage,
        reelsModel,
        liveUserModel, liveUserisloding, livenewStoryData,
        //Planner schedule post web
        isSchedulePostWebLoading,
        schedulePostWebModel,
        schedulePostWeb,
        isPlannerPostListLoading,
        isPlannerPostListAllFetch,
        plannerPostListPage,
        isPlannerPostListLoadingMore,
        plannerPostData,
        plannerListPostModel, isAIGeneratingMassageloading,
        aiGenerateMassageOrCommentModel,
      ];

  HomeFeedState copyWith({
    List<PostData>? posts,
    bool? allFetch,
    bool? isLoadingMore,
    bool? homeFeedLoading,
    PostResponseModel? postResponsemodel,
    int? postPage,
    int? postId,
    GetCommentModel? getcommentModel,
    bool? isCommentloding,
    TextEditingController? commentTextController,
    TextEditingController? replycommentTextController,
    List<VideoData>? video,
    bool? allVideoFetch,
    bool? isVideoLoadingMore,
    bool? homeFeedVideoLoading,
    VideoResponseModel? videoResponseModel,
    int? videoPage,
    bool? isDeleteLoading,
    // SavePost
    List<SavePostData>? savedposts,
    bool? isSavePostLoadingMore,
    bool? isSavePostloding,
    bool? savePosthasMore,
    SavePostModel? savepostModel,
    int? savepospage,
    bool? isSavePostLiked,
    bool? savePostallFetch,
    bool? isdiscoverloding,
    bool? isDiscoverLoadingMore,
    bool? isDiscoverhasMore,
    int? isDiscoverpage,
    bool? isDiscoverallFetch,
    List<PostData>? isDiscoverposts,
    PostResponseModel? getAllPostModel,
    bool? anyliticsLoading,
    AnalyticsModel? analyticsModel,
    // get User Profile by Id
    bool? profileByIdLoading,
    UserProfileDetailModel? userProfile,
    List<PostData>? isuserProfileposts,
    int? userpostbyIdPage,
    bool? profilPosteByIdLoading,
    bool? profilPosteByIdLoadingmore,
    bool? profilPosteByIdallFetch,
    PostResponseModel? getAllUserPostbyidModel,
    // Get User Text Post by Id
    List<UserTextPostData>? getUserByIdTextPostData,
    bool? getUserByIdTextPostLoadingMore,
    bool? getUserByIdTextPostAllFetch,
    int? getUserByIdTextPostPage,
    bool? getUserByIdTextPostLoading,
    // get User Profile
    bool? getProfileLoading,
    UserProfileDetailModel? getProfileModel,
    PostResponseModel? getProfilePostmodel,
    List<PostData>? getProfilePost,
    int? getProfilepostpage,
    bool? getprofilrPosteLoading,
    bool? getprofilPosteLoadingmore,
    bool? getprofilPostedallFetch,
    // get user text post
    bool? getuserTextPostLoading,
    GetUserTextPostModel? getUserTextPostModel,
    List<UserTextPostData>? getUserTextPostData,
    int? getUserTextPostPage,
    bool? getUserTextPostLoadingMore,
    bool? getUserTextPostAllFetch,
    List<VideoData>? getProfilevideo,
    bool? getProfileallVideoFetch,
    bool? getProfileisVideoLoadingMore,
    bool? getProfileVideoLoading,
    VideoResponseModel? getProfilevideoResponseModel,
    int? getProfilevideoPage,
    List<VideoData>? getProfileByIDvideo,
    bool? getProfileallByIDVideoFetch,
    bool? getProfileisVideoByIDLoadingMore,
    bool? getProfileVideoByIDLoading,
    VideoResponseModel? getProfilevideoByIDResponseModel,
    int? getProfilevideoByIDPage,
    // Get PostState
    UserProfileModel? userProfileModel,
    bool? isGetPostLoading,
    String? getPostError,
    bool? storyisloding,
    // List<SingleStoryData>? singleStoryData,
    NewStoryModel? newStoryModel,
    List<NewStory>? newStoryData,
    List<Stories>? newstory,
    UploadStoryModel? uploadStoryModel,
    int? tagpage,
    TagPostmodel? tagPostmodel,
    List<TagPostData>? tagPostData,
    bool? istagLoadingMore,
    bool? isloding,
    List<FolllowData>? postLikeList,
    BlockUserModel? blockUserModel,
    bool? isBlockLoading,
    List<BlockedUsersData>? blockedUsersList,
    TextEditingController? searchController,
    // hash tag post
    HashtagPostModel? hashtagPostModel,
    List<HashtagPostData>? hashtagPostData,
    // get Draft Post
    bool? isGetDraftPostLoading,
    int? draftPostPage,
    List<DraftPostData>? draftPosts,
    bool? isdeleteDraftPostLoading,
    bool? isGetDraftPostLoadingMore,
    bool? isUploadDraftPostLoading,
    DraftPostModel? draftPostModel,

    //
    PostShareModel? postShareModel,
    SharePostMessageModel? sharePostMessageModel,
    bool? isPostShareLoading,
    String? searchQuery,
    List<Map<String, dynamic>>? searchHistory,
    List<SearchUserData>? searchuserList,

    //Notification Post
    List<GetNotificationPostData>? getpostbyIdData,
    bool? fetchPostLoading,
    GetPostbyIdModel? getPostbyIdModel,

    //Deep Link Post
    bool? isDeepLinkPostLoading,
    DeepLinkPostModel? deepLinkPostModel,
    String? errorMessage,

    //Reels
    List<ReelData>? reels,
    bool? isReelsLoading,
    bool? isReelsLoadingMore,
    bool? isReelsAllFetch,
    int? reelsPage,
    ReelData? reelsModel,
    //Live
    LiveUserModel? liveUserModel,
    bool? liveUserisloding,
    List<LiveNewStory>? livenewStoryData,
    // planner schedule post web
    bool? isSchedulePostWebLoading,
    List<ScheduledPostWebData>? schedulePostWeb,
    ScheduledPostsWebModel? schedulePostWebModel,
    bool? isPlannerPostListLoading,
    bool? isPlannerPostListLoadingMore,
    bool? isPlannerPostListAllFetch,
    int? plannerPostListPage,
    PlannerListPostModel? plannerListPostModel,
    List<PlannerPostData>? plannerPostData,
    bool? isAIGeneratingMassageloading,
    AiGenerateMassageOrComment? aiGenerateMassageOrCommentModel,
  }) {
    return HomeFeedState(
      posts: posts ?? this.posts,
      postId: postId ?? this.postId,
      allFetch: allFetch ?? this.allFetch,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      homeFeedLoading: homeFeedLoading ?? this.homeFeedLoading,
      postResponsemodel: postResponsemodel ?? this.postResponsemodel,
      postPage: postPage ?? this.postPage,
      getcommentModel: getcommentModel ?? this.getcommentModel,
      isCommentloding: isCommentloding ?? this.isCommentloding,
      commentTextController: commentTextController ?? this.commentTextController,
      replycommentTextController: replycommentTextController ?? this.replycommentTextController,
      video: video ?? this.video,
      allVideoFetch: allVideoFetch ?? this.allVideoFetch,
      isVideoLoadingMore: isVideoLoadingMore ?? this.isVideoLoadingMore,
      homeFeedVideoLoading: homeFeedVideoLoading ?? this.homeFeedVideoLoading,
      videoResponseModel: videoResponseModel ?? this.videoResponseModel,
      videoPage: videoPage ?? this.videoPage,
      isDeleteLoading: isDeleteLoading ?? this.isDeleteLoading,
      //Save Post
      savedposts: savedposts ?? this.savedposts,
      isSavePostLoadingMore: isSavePostLoadingMore ?? this.isSavePostLoadingMore,
      isSavePostloding: isSavePostloding ?? this.isSavePostloding,
      savePosthasMore: savePosthasMore ?? this.savePosthasMore,
      savepostModel: savepostModel ?? this.savepostModel,
      savepospage: savepospage ?? this.savepospage,
      isSavePostLiked: isSavePostLiked ?? this.isSavePostLiked,
      savePostallFetch: savePostallFetch ?? this.savePostallFetch,
      isdiscoverloding: isdiscoverloding ?? this.isdiscoverloding,
      isDiscoverLoadingMore: isDiscoverLoadingMore ?? this.isDiscoverLoadingMore,
      isDiscoverhasMore: isDiscoverhasMore ?? this.isDiscoverhasMore,
      isDiscoverpage: isDiscoverpage ?? this.isDiscoverpage,
      isDiscoverallFetch: isDiscoverallFetch ?? this.isDiscoverallFetch,
      isDiscoverposts: isDiscoverposts ?? this.isDiscoverposts,
      getAllPostModel: getAllPostModel ?? this.getAllPostModel,
      // Analytics
      anyliticsLoading: anyliticsLoading ?? this.anyliticsLoading,
      analyticsModel: analyticsModel ?? this.analyticsModel,
      // get User Profile by Id
      profileByIdLoading: profileByIdLoading ?? this.profileByIdLoading,
      userProfile: userProfile ?? this.userProfile,
      isuserProfileposts: isuserProfileposts ?? this.isuserProfileposts,
      userpostbyIdPage: userpostbyIdPage ?? this.userpostbyIdPage,
      profilPosteByIdLoading: profilPosteByIdLoading ?? this.profilPosteByIdLoading,
      profilPosteByIdLoadingmore: profilPosteByIdLoadingmore ?? this.profilPosteByIdLoadingmore,
      profilPosteByIdallFetch: profilPosteByIdallFetch ?? this.profilPosteByIdallFetch,
      getAllUserPostbyidModel: getAllUserPostbyidModel ?? this.getAllUserPostbyidModel,
      // Get User Text Post by Id
      getUserByIdTextPostData: getUserByIdTextPostData ?? this.getUserByIdTextPostData,
      getUserByIdTextPostLoadingMore: getUserByIdTextPostLoadingMore ?? this.getUserByIdTextPostLoadingMore,
      getUserByIdTextPostAllFetch: getUserByIdTextPostAllFetch ?? this.getUserByIdTextPostAllFetch,
      getUserByIdTextPostPage: getUserByIdTextPostPage ?? this.getUserByIdTextPostPage,
      getUserByIdTextPostLoading: getUserByIdTextPostLoading ?? this.getUserByIdTextPostLoading,
      // get User Profile
      getProfileLoading: getProfileLoading ?? this.getProfileLoading,
      // getProfileModel: getProfileModel ?? this.getProfileModel,
      getProfilePostmodel: getProfilePostmodel ?? this.getProfilePostmodel,
      getProfilePost: getProfilePost ?? this.getProfilePost,
      getProfilepostpage: getProfilepostpage ?? this.getProfilepostpage,
      getprofilrPosteLoading: getprofilrPosteLoading ?? this.getprofilrPosteLoading,
      getprofilPosteLoadingmore: getprofilPosteLoadingmore ?? this.getprofilPosteLoadingmore,
      getprofilPostedallFetch: getprofilPostedallFetch ?? this.getprofilPostedallFetch,
      // get user text post
      getuserTextPostLoading: getuserTextPostLoading ?? this.getuserTextPostLoading,
      getUserTextPostModel: getUserTextPostModel ?? this.getUserTextPostModel,
      getUserTextPostData: getUserTextPostData ?? this.getUserTextPostData,
      getUserTextPostPage: getUserTextPostPage ?? this.getUserTextPostPage,
      getUserTextPostLoadingMore: getUserTextPostLoadingMore ?? this.getUserTextPostLoadingMore,
      getUserTextPostAllFetch: getUserTextPostAllFetch ?? this.getUserTextPostAllFetch,

      getProfilevideo: getProfilevideo ?? this.getProfilevideo,
      getProfileallVideoFetch: getProfileallVideoFetch ?? this.getProfileallVideoFetch,
      getProfileisVideoLoadingMore: getProfileisVideoLoadingMore ?? this.getProfileisVideoLoadingMore,
      getProfileVideoLoading: getProfileVideoLoading ?? this.getProfileVideoLoading,
      getProfilevideoResponseModel: getProfilevideoResponseModel ?? this.getProfilevideoResponseModel,
      getProfilevideoPage: getProfilevideoPage ?? this.getProfilevideoPage,
      getProfileByIDvideo: getProfilevideo ?? this.getProfilevideo,
      getProfileallByIDVideoFetch: getProfileallVideoFetch ?? this.getProfileallVideoFetch,
      getProfileisVideoByIDLoadingMore: getProfileisVideoLoadingMore ?? this.getProfileisVideoLoadingMore,
      getProfileVideoByIDLoading: getProfileVideoByIDLoading ?? this.getProfileVideoByIDLoading,
      getProfilevideoByIDResponseModel: getProfilevideoResponseModel ?? this.getProfilevideoResponseModel,
      getProfilevideoByIDPage: getProfilevideoPage ?? this.getProfilevideoPage,
      // Get PostState
      userProfileModel: userProfileModel ?? this.userProfileModel,
      isGetPostLoading: isGetPostLoading ?? this.isGetPostLoading,
      getPostError: getPostError ?? this.getPostError,
      newStoryModel: newStoryModel ?? this.newStoryModel,
      newStoryData: newStoryData ?? this.newStoryData,
      newstory: newstory ?? this.newstory,
      uploadStoryModel: uploadStoryModel ?? this.uploadStoryModel,
      istagLoadingMore: istagLoadingMore ?? this.istagLoadingMore,
      isloding: isloding ?? this.isloding,
      tagPostData: tagPostData ?? this.tagPostData,
      tagPostmodel: tagPostmodel ?? this.tagPostmodel,
      tagpage: tagpage ?? this.tagpage,
      postLikeList: postLikeList ?? this.postLikeList,
      blockedUsersList: blockedUsersList ?? this.blockedUsersList,
      blockUserModel: blockUserModel ?? this.blockUserModel,
      isBlockLoading: isBlockLoading ?? this.isBlockLoading,
      searchController: searchController ?? this.searchController,
      hashtagPostModel: hashtagPostModel ?? this.hashtagPostModel,
      hashtagPostData: hashtagPostData ?? this.hashtagPostData,
      // Get Draft Post
      draftPosts: draftPosts ?? this.draftPosts,
      draftPostPage: draftPostPage ?? this.draftPostPage,
      draftPostModel: draftPostModel ?? this.draftPostModel,
      isGetDraftPostLoading: isGetDraftPostLoading ?? this.isGetDraftPostLoading,
      isdeleteDraftPostLoading: isdeleteDraftPostLoading ?? this.isdeleteDraftPostLoading,
      isUploadDraftPostLoading: isUploadDraftPostLoading ?? this.isUploadDraftPostLoading,
      isGetDraftPostLoadingMore: isGetDraftPostLoadingMore ?? this.isGetDraftPostLoadingMore,

      // Post Share
      postShareModel: postShareModel ?? this.postShareModel,
      sharePostMessageModel: sharePostMessageModel ?? this.sharePostMessageModel,
      isPostShareLoading: isPostShareLoading ?? this.isPostShareLoading,
      searchQuery: searchQuery ?? this.searchQuery,
      searchHistory: searchHistory ?? this.searchHistory,
      searchuserList: searchuserList ?? this.searchuserList,

      //Notification Post
      getpostbyIdData: getpostbyIdData ?? this.getpostbyIdData,
      fetchPostLoading: fetchPostLoading ?? this.fetchPostLoading,
      getPostbyIdModel: getPostbyIdModel ?? this.getPostbyIdModel,
      //Deep Link Post
      isDeepLinkPostLoading: isDeepLinkPostLoading ?? this.isDeepLinkPostLoading,
      deepLinkPostModel: deepLinkPostModel ?? this.deepLinkPostModel,
      errorMessage: errorMessage ?? this.errorMessage,

      //Reels
      reels: reels ?? this.reels,
      isReelsLoading: isReelsLoading ?? this.isReelsLoading,
      isReelsLoadingMore: isReelsLoadingMore ?? this.isReelsLoadingMore,
      isReelsAllFetch: isReelsAllFetch ?? this.isReelsAllFetch,
      reelsPage: reelsPage ?? this.reelsPage,
      reelsModel: reelsModel ?? this.reelsModel,
      liveUserModel: liveUserModel ?? this.liveUserModel,
      liveUserisloding: liveUserisloding ?? this.liveUserisloding,
      livenewStoryData: livenewStoryData ?? this.livenewStoryData,
      // Planner Schedule Post web
      isSchedulePostWebLoading: isSchedulePostWebLoading ?? this.isSchedulePostWebLoading,
      schedulePostWeb: schedulePostWeb ?? this.schedulePostWeb,
      schedulePostWebModel: schedulePostWebModel ?? this.schedulePostWebModel,
      isPlannerPostListLoading: isPlannerPostListLoading ?? this.isPlannerPostListLoading,
      isPlannerPostListLoadingMore: isPlannerPostListLoadingMore ?? this.isPlannerPostListLoadingMore,
      isPlannerPostListAllFetch: isPlannerPostListAllFetch ?? this.isPlannerPostListAllFetch,
      plannerPostListPage: plannerPostListPage ?? this.plannerPostListPage,
      plannerListPostModel: plannerListPostModel ?? this.plannerListPostModel,
      plannerPostData: plannerPostData ?? this.plannerPostData,
      isAIGeneratingMassageloading: isAIGeneratingMassageloading ?? this.isAIGeneratingMassageloading,
      aiGenerateMassageOrCommentModel: aiGenerateMassageOrCommentModel ?? this.aiGenerateMassageOrCommentModel,
    );
  }
}
