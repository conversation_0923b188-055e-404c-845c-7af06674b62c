part of 'home_feed_bloc.dart';

sealed class HomeFeedEvent extends Equatable {}

class HomeFeedInitial extends HomeFeedEvent {
  @override
  List<Object?> get props => [];
}

class GetAllPostApiEvent extends HomeFeedEvent {
  final int page;

  GetAllPostApiEvent({required this.page});

  @override
  List<Object?> get props => [page];
}

class GetAllVideoApiEvent extends HomeFeedEvent {
  final int page;

  GetAllVideoApiEvent({required this.page});

  @override
  List<Object?> get props => [page];
}

class GetAllPostProgressiveEvent extends HomeFeedEvent {
  final int page;

  GetAllPostProgressiveEvent({required this.page});

  @override
  List<Object?> get props => [page];
}

class GetAllVideoProgressiveEvent extends HomeFeedEvent {
  final int page;

  GetAllVideoProgressiveEvent({required this.page});

  @override
  List<Object?> get props => [page];
}

class LikePostSocketEvent extends HomeFeedEvent {
  final int postId;

  LikePostSocketEvent({required this.postId});

  @override
  List<Object?> get props => [postId];
}

class UpdatePostLikesEvent extends HomeFeedEvent {
  final int postId;
  final int likes;
  final bool? isLiked;

  UpdatePostLikesEvent({
    required this.postId,
    required this.likes,
    this.isLiked,
  });
  @override
  List<Object?> get props => [postId, likes, isLiked];
}

class GetCommentApiEvent extends HomeFeedEvent {
  final int postId;

  GetCommentApiEvent({required this.postId});

  @override
  List<Object?> get props => [postId];
}

class CommentPostSocketEvent extends HomeFeedEvent {
  final String commentText;
  final String postId;

  CommentPostSocketEvent({
    required this.commentText,
    required this.postId,
  });
  @override
  List<Object?> get props => [commentText, postId];
}

class UpdatePostCommentEvent extends HomeFeedEvent {
  final dynamic data;
  final dynamic id;
  final dynamic commentText;
  final dynamic createdAt;
  final dynamic user;
  final dynamic likesCount;
  final dynamic commentCount;
  final dynamic postId;
  final dynamic latestcomment;

  UpdatePostCommentEvent({
    required this.data,
    required this.id,
    required this.commentText,
    required this.createdAt,
    required this.user,
    required this.likesCount,
    required this.commentCount,
    required this.postId,
    required this.latestcomment,
  });
  @override
  List<Object?> get props => [data, id, commentText, createdAt, user, likesCount, commentCount, postId, latestcomment];
}

class ReplycommentSocketEvent extends HomeFeedEvent {
  final String commentId;
  final String replyText;

  ReplycommentSocketEvent({
    required this.commentId,
    required this.replyText,
  });
  @override
  List<Object?> get props => [commentId, replyText];
}

class UpdateReplyCommenEvent extends HomeFeedEvent {
  final dynamic commentid;
  final dynamic replyid;
  final dynamic likecount;
  final dynamic createdAt;
  final dynamic user;
  final dynamic replyCommentText;
  final bool isLiked;

  UpdateReplyCommenEvent({
    this.commentid,
    required this.replyid,
    required this.likecount,
    required this.createdAt,
    required this.user,
    required this.replyCommentText,
    this.isLiked = false,
  });
  @override
  List<Object?> get props => [commentid, likecount, createdAt, user, replyCommentText, isLiked, replyid];
}

class ReplayCommentLikeSocketEvent extends HomeFeedEvent {
  final int commentId;

  ReplayCommentLikeSocketEvent({
    required this.commentId,
  });
  @override
  List<Object?> get props => [commentId];
}

class UpdateReplyCommenLikeEvent extends HomeFeedEvent {
  final dynamic commentid;
  final dynamic likecount;
  final dynamic isLiked;

  UpdateReplyCommenLikeEvent({required this.commentid, required this.likecount, this.isLiked});
  @override
  List<Object?> get props => [commentid, likecount, isLiked];
}

class UpdateCommenLikeEvent extends HomeFeedEvent {
  final dynamic commentid;
  final dynamic isliked;
  final dynamic likecount;

  UpdateCommenLikeEvent({
    required this.commentid,
    this.isliked,
    required this.likecount,
  });
  @override
  List<Object?> get props => [commentid, isliked, likecount];
}

class LikeCommentSocketEvent extends HomeFeedEvent {
  final int commentId;

  LikeCommentSocketEvent({required this.commentId});
  @override
  List<Object?> get props => [commentId];
}

class DeleteCommentApiEvent extends HomeFeedEvent {
  final int commentId;
  final String postId;

  DeleteCommentApiEvent({required this.commentId, required this.postId});
  @override
  List<Object?> get props => [commentId, postId];
}

class RefreshCommentGetApiEvent extends HomeFeedEvent {
  final String postId;

  RefreshCommentGetApiEvent({required this.postId});

  @override
  List<Object?> get props => [postId];
}

class DeletePostApiEvent extends HomeFeedEvent {
  final int postId;
  final int index;
  final bool? userpost;
  final bool? userByIDpost;
  final bool? userVideo;
  final bool? userByIDvideo;
  final bool? isVideo;
  final bool? isPost;
  final bool? isNavigate;
  final BuildContext? context;
  final bool? isprofilrpostDelete;
  final bool? isTextPost;
  final bool? isNotificationPost;

  DeletePostApiEvent({
    required this.postId,
    required this.index,
    this.userByIDpost,
    this.isNavigate,
    this.userpost,
    this.userByIDvideo,
    this.userVideo,
    this.isVideo,
    this.isPost,
    this.context,
    this.isTextPost,
    this.isprofilrpostDelete,
    this.isNotificationPost,
  });

  @override
  List<Object?> get props => [
        postId,
        index,
        userByIDpost,
        userpost,
        userVideo,
        userByIDvideo,
        isVideo,
        isNavigate,
        isPost,
        context,
        isTextPost,
        isprofilrpostDelete,
        isNotificationPost
      ];
}

class UpdateSavedPostEvent extends HomeFeedEvent {
  final int postId;
  final bool? isSaved;

  UpdateSavedPostEvent({
    required this.postId,
    this.isSaved,
  });
  @override
  List<Object?> get props => [postId, isSaved];
}

class SavedPostSocketEvent extends HomeFeedEvent {
  final String postId;

  SavedPostSocketEvent({
    required this.postId,
  });
  @override
  List<Object?> get props => [postId];
}

class SavedPostApiEvent extends HomeFeedEvent {
  final int? page;
  SavedPostApiEvent({required this.page});
  @override
  List<Object?> get props => [page];
}

class DiscoverPostApiEvent extends HomeFeedEvent {
  final int page;
  DiscoverPostApiEvent({required this.page});
  @override
  List<Object> get props => [page];
}

class AnalyticsApiEvent extends HomeFeedEvent {
  final int postId;
  AnalyticsApiEvent({required this.postId});

  @override
  List<Object> get props => [postId];
}

//============== MARK:User Profile by Id
class GetUserProfilebyIdApi extends HomeFeedEvent {
  final int userId;

  GetUserProfilebyIdApi({required this.userId});

  @override
  List<Object?> get props => [userId];
}

class GetUserPostApiIdEvent extends HomeFeedEvent {
  final int page;

  final int? userId;

  GetUserPostApiIdEvent({required this.page, this.userId});

  @override
  List<Object?> get props => [page, userId];
}

class GetUserVideoByIdApiEvent extends HomeFeedEvent {
  final int page;

  final int? userId;

  GetUserVideoByIdApiEvent({required this.page, this.userId});

  @override
  List<Object?> get props => [page, userId];
}

class GetUserByIdTextPostApiIdEvent extends HomeFeedEvent {
  final int page;

  final int? userId;

  GetUserByIdTextPostApiIdEvent({required this.page, this.userId});

  @override
  List<Object?> get props => [page, userId];
}

class EditTextPostAPIEvent extends HomeFeedEvent {
  final int postId;
  final String title;
  final String description;

  EditTextPostAPIEvent({required this.postId, required this.title, required this.description});

  @override
  List<Object?> get props => [postId, title, description];
}

// ==================== MARK:Get User Profile
// class FetchUserProfileEvent extends HomeFeedEvent {
//   final int userId;

//   FetchUserProfileEvent({
//     required this.userId,
//   });

//   @override
//   List<Object?> get props => [userId];
// }

class FetchUserProfilePostEvent extends HomeFeedEvent {
  final int page;

  FetchUserProfilePostEvent({required this.page});

  @override
  List<Object?> get props => [page];
}

class FetchUserProfileVideoEvent extends HomeFeedEvent {
  final int page;

  FetchUserProfileVideoEvent({required this.page});

  @override
  List<Object?> get props => [page];
}

class ProfileDeletePostApiEvent extends HomeFeedEvent {
  final String postId;
  final int index;

  ProfileDeletePostApiEvent({required this.postId, required this.index});

  @override
  List<Object?> get props => [postId, index];
}

class GetUserTextPostEvent extends HomeFeedEvent {
  final int page;
  GetUserTextPostEvent({required this.page});
  @override
  List<Object> get props => [page];
}

//MARK: Edit Post EVENT
class UpdatePostApiEvent extends HomeFeedEvent {
  final String postId;
  final String title;
  final String description;
  final bool isvideo;

  UpdatePostApiEvent({required this.postId, required this.title, required this.description, required this.isvideo});
  @override
  List<Object?> get props => [postId, title, description, isvideo];
}

class GetProfilePostEvent extends HomeFeedEvent {
  @override
  List<Object?> get props => [];
}

//================================MARK: Story Event
class GetAllStoryApiEvent extends HomeFeedEvent {
  final int storyPage;
  GetAllStoryApiEvent({
    required this.storyPage,
  });
  @override
  List<Object?> get props => [storyPage];
}

class GetSingleStoryApiEvent extends HomeFeedEvent {
  final String userId;
  GetSingleStoryApiEvent({
    required this.userId,
  });
  @override
  List<Object?> get props => [userId];
}

class UploadStoryApiEvent extends HomeFeedEvent {
  final File uploadFiles;
  final String music;
  final String title;
  final bool isInstagram;
  final bool isFacebook;

  UploadStoryApiEvent({
    required this.uploadFiles,
    required this.music,
    required this.title,
    required this.isInstagram,
    required this.isFacebook,
  });
  @override
  List<Object?> get props => [uploadFiles, music, title, isInstagram, isFacebook];
}

class DeleteStoryEvent extends HomeFeedEvent {
  final int storyId;
  DeleteStoryEvent({required this.storyId});
  @override
  List<Object?> get props => [storyId];
}

class StoryLikeSocketEvent extends HomeFeedEvent {
  final int storyId;
  final int? pageIndex;
  final int? storyIndex;

  StoryLikeSocketEvent({required this.storyId, this.pageIndex, this.storyIndex});

  @override
  List<Object?> get props => [storyId];
}

class UpdateStoryLikesEvent extends HomeFeedEvent {
  final int storyId;
  final bool? isLiked;
  final int? pageIndex;
  final int? storyIndex;

  UpdateStoryLikesEvent({required this.storyId, this.isLiked, this.pageIndex, this.storyIndex});
  @override
  List<Object?> get props => [storyId, isLiked];
}

class GetNewStoryApiEvent extends HomeFeedEvent {
  GetNewStoryApiEvent();
  @override
  List<Object?> get props => [];
}

class GetTagPostApi extends HomeFeedEvent {
  final int tagpage;
  GetTagPostApi({required this.tagpage});
  @override
  List<Object?> get props => [tagpage];
}

class FollowUserLikeListSocketEvent extends HomeFeedEvent {
  final int userId;
  FollowUserLikeListSocketEvent({
    required this.userId,
  });
  @override
  List<Object?> get props => [userId];
}

class UpdateFollowUserLikeListEvent extends HomeFeedEvent {
  final bool isFollowing;
  final int userId;
  final String? type;
  final int? followingcount;

  UpdateFollowUserLikeListEvent({required this.isFollowing, required this.userId, this.type, this.followingcount});
  @override
  List<Object?> get props => [isFollowing, userId, type, followingcount];
}

class FollowUserSocketEvent extends HomeFeedEvent {
  final int userId;
  FollowUserSocketEvent({required this.userId});
  @override
  List<Object?> get props => [userId];
}

class UpdateFollowUserEvent extends HomeFeedEvent {
  final bool isFollowing;
  final int userId;

  UpdateFollowUserEvent({
    required this.isFollowing,
    required this.userId,
  });
  @override
  List<Object?> get props => [isFollowing, userId];
}

class BlockUserApiEvent extends HomeFeedEvent {
  final String userId;
  final BuildContext context;
  final bool isblocked;

  BlockUserApiEvent({required this.userId, required this.context, required this.isblocked});
  @override
  List<Object?> get props => [userId, context, isblocked];
}

class GetblockedusersListApiEvent extends HomeFeedEvent {
  GetblockedusersListApiEvent();
  @override
  List<Object?> get props => [];
}

class DiscoverHashtagPostListEvent extends HomeFeedEvent {
  final int hashtagId;
  DiscoverHashtagPostListEvent({required this.hashtagId});
  @override
  List<Object> get props => [hashtagId];
}

//MARK: Draft Post
class GetDraftPostAPIEvent extends HomeFeedEvent {
  final int draftPostPage;
  GetDraftPostAPIEvent({required this.draftPostPage});
  @override
  List<Object> get props => [draftPostPage];
}

class DeleteDraftPostAPIEvent extends HomeFeedEvent {
  final int draftPostId;
  final BuildContext context;
  DeleteDraftPostAPIEvent({required this.draftPostId, required this.context});
  @override
  List<Object> get props => [draftPostId, context];
}

class UploadDraftPostAPIEvent extends HomeFeedEvent {
  final int draftPostId;
  final BuildContext context;
  UploadDraftPostAPIEvent({required this.draftPostId, required this.context});
  @override
  List<Object> get props => [draftPostId, context];
}

class PostShareEvent extends HomeFeedEvent {
  final BuildContext context;
  final String postId;
  final String shareType;
  final int toUserId;

  PostShareEvent(this.context, {required this.postId, required this.shareType, required this.toUserId});

  @override
  List<Object> get props => [context, postId, shareType, toUserId];
}

class SharePostMessageEvent extends HomeFeedEvent {
  final String postId;
  final List<int> toUserId;

  SharePostMessageEvent({required this.postId, required this.toUserId});

  @override
  List<Object> get props => [postId, toUserId];
}

class GetNotificationPostEvent extends HomeFeedEvent {
  final String postId;

  GetNotificationPostEvent({required this.postId});

  @override
  List<Object> get props => [postId];
}

class FetchDeepLinkPostEvent extends HomeFeedEvent {
  final String postId;
  final String? shareType;

  FetchDeepLinkPostEvent({required this.postId, this.shareType});

  @override
  List<Object?> get props => [postId, shareType];
}

class UserSearchQueryChanged extends HomeFeedEvent {
  final String query;
  UserSearchQueryChanged(this.query);

  @override
  List<Object?> get props => [query];
}

class ClearUserSearchHistory extends HomeFeedEvent {
  ClearUserSearchHistory();
  @override
  List<Object?> get props => [];
}

class RemoveUserSearchItem extends HomeFeedEvent {
  final int index;
  RemoveUserSearchItem(this.index);

  @override
  List<Object?> get props => [index];
}

class SharePostSearchUserListEvent extends HomeFeedEvent {
  final String searchtext;
  SharePostSearchUserListEvent({required this.searchtext});

  @override
  List<Object> get props => [searchtext];
}

class GetLivestoryApiEvent extends HomeFeedEvent {
  GetLivestoryApiEvent();
  @override
  List<Object?> get props => [];
}

class GetScheduledPostWebEvent extends HomeFeedEvent {
  GetScheduledPostWebEvent();
  @override
  List<Object?> get props => [];
}

class GetPlannerPostListEvent extends HomeFeedEvent {
  final String? platform;
  final int page;
  GetPlannerPostListEvent({this.platform, required this.page});
  @override
  List<Object?> get props => [platform, page];
}

class AIgenerateCommentEvent extends HomeFeedEvent {
  final String massageText;
  final BuildContext context;
  AIgenerateCommentEvent({required this.massageText, required this.context});

  @override
  List<Object> get props => [massageText, context];
}

class ClearAIGeneratedCommentEvent extends HomeFeedEvent {
  @override
  List<Object?> get props => [];
}
