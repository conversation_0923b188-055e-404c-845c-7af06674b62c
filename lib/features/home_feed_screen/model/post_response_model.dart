import 'package:json_annotation/json_annotation.dart';

part 'post_response_model.g.dart';

PostResponseModel deserializePostResponseModel(Map<String, dynamic> json) => PostResponseModel.fromJson(json);

// Root Response
@JsonSerializable(explicitToJson: true)
class PostResponseModel {
  final int count;

  @JsonKey(name: 'next')
  final String? nextPage;

  @JsonKey(name: 'previous')
  final String? previousPage;

  final Results results;

  PostResponseModel({
    required this.count,
    this.nextPage,
    this.previousPage,
    required this.results,
  });

  factory PostResponseModel.fromJson(Map<String, dynamic> json) => _$PostResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$PostResponseModelToJson(this);

  PostResponseModel copyWith({
    int? count,
    String? nextPage,
    String? previousPage,
    Results? results,
  }) {
    return PostResponseModel(
      count: count ?? this.count,
      nextPage: nextPage ?? this.nextPage,
      previousPage: previousPage ?? this.previousPage,
      results: results ?? this.results,
    );
  }
}

// Results Class
@JsonSerializable(explicitToJson: true)
class Results {
  final bool status;
  final String message;

  @JsonKey(name: 'data')
  final List<PostData> posts;

  Results({
    required this.status,
    required this.message,
    required this.posts,
  });

  factory Results.fromJson(Map<String, dynamic> json) => _$ResultsFromJson(json);

  Map<String, dynamic> toJson() => _$ResultsToJson(this);

  Results copyWith({
    bool? status,
    String? message,
    List<PostData>? posts,
  }) {
    return Results(
      status: status ?? this.status,
      message: message ?? this.message,
      posts: posts ?? this.posts,
    );
  }
}

// PostData Class
@JsonSerializable(explicitToJson: true)
class PostData {
  final int id;
  final String title;
  final String description;
  final String location;
  final int likes;
  final int dislikes;

  @JsonKey(name: 'comments_count')
  final int commentsCount;

  @JsonKey(name: 'tagged_in')
  final dynamic taggedIn;

  @JsonKey(name: 'created_at')
  final String createdAt;

  final List<String> files;
  final int width;
  final int height;

  @JsonKey(name: 'thumbail_files')
  final List<String>? thumbnailFiles;

  @JsonKey(name: 'latest_comment')
  final String latestComment;

  final User user;

  @JsonKey(name: 'is_liked')
  final bool isLiked;

  @JsonKey(name: 'is_saved')
  final bool isSaved;
  @JsonKey(name: 'is_text_post')
  final bool isTextPost;

  PostData({
    required this.id,
    required this.title,
    required this.description,
    required this.location,
    required this.likes,
    required this.dislikes,
    required this.commentsCount,
    this.taggedIn,
    required this.createdAt,
    required this.files,
    required this.thumbnailFiles,
    required this.latestComment,
    required this.user,
    required this.isLiked,
    required this.isSaved,
    required this.width,
    required this.height,
    required this.isTextPost,
  });

  factory PostData.fromJson(Map<String, dynamic> json) => _$PostDataFromJson(json);

  Map<String, dynamic> toJson() => _$PostDataToJson(this);

  PostData copyWith({
    int? id,
    String? title,
    String? description,
    String? location,
    int? likes,
    int? dislikes,
    int? commentsCount,
    Map<String, dynamic>? taggedIn,
    String? createdAt,
    List<String>? files,
    List<String>? thumbnailFiles,
    String? latestComment,
    User? user,
    bool? isLiked,
    bool? isSaved,
    int? width,
    int? height,
    bool? isTextPost,
  }) {
    return PostData(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
      likes: likes ?? this.likes,
      dislikes: dislikes ?? this.dislikes,
      commentsCount: commentsCount ?? this.commentsCount,
      taggedIn: taggedIn ?? this.taggedIn,
      createdAt: createdAt ?? this.createdAt,
      files: files ?? this.files,
      thumbnailFiles: thumbnailFiles ?? this.thumbnailFiles,
      latestComment: latestComment ?? this.latestComment,
      user: user ?? this.user,
      isLiked: isLiked ?? this.isLiked,
      isSaved: isSaved ?? this.isSaved,
      width: width ?? this.width,
      height: height ?? this.height,
      isTextPost: isTextPost ?? this.isTextPost,
    );
  }
}

// User Class
@JsonSerializable()
class User {
  @JsonKey(name: 'user_id')
  final int userId;

  final String username;
  final String name;

  @JsonKey(name: 'profile_image')
  final String profileImage;

  User({
    required this.userId,
    required this.username,
    required this.name,
    required this.profileImage,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  Map<String, dynamic> toJson() => _$UserToJson(this);

  User copyWith({
    int? userId,
    String? username,
    String? name,
    String? profileImage,
  }) {
    return User(
      userId: userId ?? this.userId,
      username: username ?? this.username,
      name: name ?? this.name,
      profileImage: profileImage ?? this.profileImage,
    );
  }
}
