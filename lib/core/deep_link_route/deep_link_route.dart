import 'dart:async';
import 'dart:developer';
// import 'dart:io';
import 'package:app_links/app_links.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/deep_link_post/deep_link_post.dart';
import 'package:flowkar/features/notification/page/notification_screen.dart';
import 'package:flowkar/features/sm_chat/bloc/sm_chat_bloc.dart';

class DeepLinkRoute {
  bool initialUriHandled = false;
  StreamSubscription<Uri?>? _sub;
  final AppLinks _appLinks = AppLinks();

  Future<void> initUniLinks(BuildContext context) async {
    log('Setting up deep link subscription');

    try {
      // Handle initial URI
      // if (Platform.isIOS) {
      //   if (!_initialUriHandled) {
      //     final initialUri = await _appLinks.getInitialLink();
      //     _initialUriHandled = true;

      //     if (initialUri != null) {
      //       log('Initial deep link received: ${initialUri.toString()}');
      //       log('Scheme: ${initialUri.scheme}');
      //       log('Host: ${initialUri.host}');
      //       log('Path: ${initialUri.path}');
      //       log('Query parameters: ${initialUri.queryParameters}');
      //       handleDeepLink(initialUri, context);
      //     } else {
      //       log('No initial deep link found');
      //     }
      //   }
      // }

      // Listen for incoming links
      _sub?.cancel();
      _sub = _appLinks.uriLinkStream.listen(
        (Uri? uri) {
          if (uri != null) {
            log('Stream deep link received: ${uri.toString()}');
            log('Scheme: ${uri.scheme}');
            log('Host: ${uri.host}');
            log('Path: ${uri.path}');
            log('Query parameters: ${uri.queryParameters}');
            // ignore: use_build_context_synchronously
            handleDeepLink(uri, context);
          }
        },
        onError: (err) {
          log('Error receiving deep link: $err', error: err);
        },
      );
    } catch (e) {
      log('Error initializing deep link: $e', error: e);
    }
  }

  void handleDeepLink(Uri uri, BuildContext context) {
    try {
      log('Processing deep link: ${uri.toString()}');

      if (uri.path.startsWith('/api/social-connect-deeplink/') || uri.path.startsWith('/api/social-connect/')) {
        final pathSegments = uri.pathSegments;
        if (pathSegments.length >= 3) {
          final brandId = pathSegments[2];
          log('Handling social connect deep link for brand: $brandId');

          PersistentNavBarNavigator.pushNewScreen(
            context,
            screen: SocialConnectPage(stackonScreen: true, oncallBack: () {}),
          );
          Navigator.pop(context);
        }
      } else if (uri.path.startsWith('/api/post/share/') || uri.path.startsWith('/api/share/post/')) {
        final pathSegments = uri.pathSegments;
        if (pathSegments.length >= 3) {
          final postId = pathSegments[3];
          log('Handling post share deep link for post: $postId');

          PersistentNavBarNavigator.pushNewScreen(
            context,
            withNavBar: false,
            screen: DeepLinkPostScreen(
              postId: postId,
              shareType: uri.queryParameters['share_type'],
              showCommentBottomSheet: false,
            ),
          );
        }
      } else if (uri.path.startsWith('/api/like-profile-deeplink/') || uri.path.startsWith('/api/like-profile-view/')) {
        final pathSegments = uri.pathSegments;
        if (pathSegments.length >= 3) {
          final postId = pathSegments[2];
          log('Handling like post deep link for post: $postId');

          PersistentNavBarNavigator.pushNewScreen(
            context,
            withNavBar: false,
            screen: DeepLinkPostScreen(
              postId: postId,
              shareType: uri.queryParameters['share_type'],
              showCommentBottomSheet: false,
            ),
          );
        }
      } else if (uri.path.startsWith('/api/comment-profile-deeplink/') ||
          uri.path.startsWith('/api/comment-profile-view/')) {
        final pathSegments = uri.pathSegments;
        if (pathSegments.length >= 3) {
          final postId = pathSegments[2];
          log('Handling comment profile view deep link for post: $postId');

          PersistentNavBarNavigator.pushNewScreen(
            context,
            withNavBar: false,
            screen: DeepLinkPostScreen(
              postId: postId,
              showCommentBottomSheet: true,
            ),
          );
        }
      } else if (uri.path.startsWith('/api/follow-profile-deeplink/') ||
          uri.path.startsWith('/api/follow-profile-view/')) {
        log('Handling follow profile view deep link');
        PersistentNavBarNavigator.pushNewScreen(
          context,
          withNavBar: false,
          screen: NotificationPage(),
        );
      } else if (uri.path.startsWith('/api/chat-profile-deeplink/') || uri.path.startsWith('/api/chat-profile-view/')) {
        final pathSegments = uri.pathSegments;
        if (pathSegments.length >= 3) {
          final receiverId = pathSegments[2];
          log('Handling chat profile view deep link for receiver: $receiverId');

          context.read<SmChatBloc>().add(FetchDeepLinkChatEvent(context, reciverId: receiverId));
        }
      } else {
        log('Unhandled deep link path: ${uri.path}');
      }
    } catch (e) {
      log('Error handling deep link: $e', error: e);
    }
  }

  void reinit(BuildContext context) {
    if (_sub == null || _sub!.isPaused) {
      initUniLinks(context);
    }
  }

  void dispose() {
    _sub?.cancel();
    _sub = null;
    initialUriHandled = false;
  }
}
