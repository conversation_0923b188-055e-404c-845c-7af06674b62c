import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:pip/pip.dart';

/// Service class to handle Picture-in-Picture (PIP) functionality
/// Provides platform-specific PIP configuration and state management
class PipService {
  static final PipService _instance = PipService._internal();
  factory PipService() => _instance;
  PipService._internal();

  final Pip _pip = Pip();
  bool _isInitialized = false;
  bool _isPipActive = false;

  /// Callback for PIP state changes
  Function(PipState state, String? error)? onPipStateChanged;

  /// Initialize PIP service with platform-specific configuration
  Future<bool> initialize() async {
    try {
      // Check if device supports PIP
      final isSupported = await _pip.isSupported();
      if (!isSupported) {
        debugPrint('PIP is not supported on this device');
        return false;
      }

      // Setup platform-specific PIP options
      final options = _getPlatformSpecificOptions();
      final setupResult = await _pip.setup(options);

      if (setupResult) {
        // Register state change observer
        await _pip.registerStateChangedObserver(
          PipStateChangedObserver(
            onPipStateChanged: (state, error) {
              _handlePipStateChange(state, error);
              onPipStateChanged?.call(state, error);
            },
          ),
        );

        _isInitialized = true;
        debugPrint('PIP service initialized successfully');
        return true;
      } else {
        debugPrint('Failed to setup PIP');
        return false;
      }
    } catch (e) {
      debugPrint('Error initializing PIP service: $e');
      return false;
    }
  }

  /// Get platform-specific PIP options
  PipOptions _getPlatformSpecificOptions() {
    if (Platform.isAndroid) {
      return PipOptions(
        autoEnterEnabled: true,
        aspectRatioX: 16,
        aspectRatioY: 9,
        sourceRectHintLeft: 0,
        sourceRectHintTop: 0,
        sourceRectHintRight: 1080,
        sourceRectHintBottom: 720,
        seamlessResizeEnabled: true,
        useExternalStateMonitor: true,
        externalStateMonitorInterval: 100,
      );
    } else if (Platform.isIOS) {
      return PipOptions(
        autoEnterEnabled: true,
        sourceContentView: 0,
        contentView: 0,
        preferredContentWidth: 480,
        preferredContentHeight: 270,
        controlStyle: 2, // Hide play/pause and progress bar
      );
    } else {
      return PipOptions(autoEnterEnabled: true);
    }
  }

  /// Start PIP mode
  Future<bool> startPip() async {
    if (!_isInitialized) {
      debugPrint('PIP service not initialized');
      return false;
    }

    try {
      final result = await _pip.start();
      if (result) {
        debugPrint('PIP started successfully');
        return true;
      } else {
        debugPrint('Failed to start PIP');
        return false;
      }
    } catch (e) {
      debugPrint('Error starting PIP: $e');
      return false;
    }
  }

  /// Stop PIP mode
  Future<void> stopPip() async {
    if (!_isInitialized) {
      debugPrint('PIP service not initialized');
      return;
    }

    try {
      await _pip.stop();
      debugPrint('PIP stopped');
    } catch (e) {
      debugPrint('Error stopping PIP: $e');
    }
  }

  /// Check if PIP is currently active
  Future<bool> isPipActive() async {
    if (!_isInitialized) return false;

    try {
      return await _pip.isActived();
    } catch (e) {
      debugPrint('Error checking PIP status: $e');
      return false;
    }
  }

  /// Check if device supports PIP
  Future<bool> isSupported() async {
    try {
      return await _pip.isSupported();
    } catch (e) {
      debugPrint('Error checking PIP support: $e');
      return false;
    }
  }

  /// Check if auto-enter PIP is supported
  Future<bool> isAutoEnterSupported() async {
    try {
      return await _pip.isAutoEnterSupported();
    } catch (e) {
      debugPrint('Error checking auto-enter PIP support: $e');
      return false;
    }
  }

  /// Handle PIP state changes
  void _handlePipStateChange(PipState state, String? error) {
    switch (state) {
      case PipState.pipStateStarted:
        _isPipActive = true;
        debugPrint('PIP state: Started');
        break;
      case PipState.pipStateStopped:
        _isPipActive = false;
        debugPrint('PIP state: Stopped');
        break;
      case PipState.pipStateFailed:
        _isPipActive = false;
        debugPrint('PIP state: Failed - $error');
        break;
    }
  }

  /// Update PIP configuration
  Future<bool> updateConfiguration(PipOptions options) async {
    if (!_isInitialized) {
      debugPrint('PIP service not initialized');
      return false;
    }

    try {
      return await _pip.setup(options);
    } catch (e) {
      debugPrint('Error updating PIP configuration: $e');
      return false;
    }
  }

  /// Dispose PIP service and clean up resources
  Future<void> dispose() async {
    if (!_isInitialized) return;

    try {
      await _pip.unregisterStateChangedObserver();
      await _pip.dispose();
      _isInitialized = false;
      _isPipActive = false;
      debugPrint('PIP service disposed');
    } catch (e) {
      debugPrint('Error disposing PIP service: $e');
    }
  }

  /// Get current PIP active status (cached)
  bool get isPipActiveCached => _isPipActive;

  /// Get initialization status
  bool get isInitialized => _isInitialized;
}
