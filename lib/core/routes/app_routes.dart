import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/analytics/presentation/widget/line_chart/analytics_charts.dart';
import 'package:flowkar/features/analytics/presentation/page/social_data_list.dart';
import 'package:flowkar/features/authentication/presentation/pages/user_type_selection_screen.dart';
import 'package:flowkar/features/authentication/presentation/pages/brand_registration_screen.dart';
import 'package:flowkar/features/brand/presentation/page/brand_list_screen.dart';
// import 'package:flowkar/features/chat/chat_screen.dart';
// import 'package:flowkar/features/discover/page/discover_page.dart';

import 'package:flowkar/features/analytics/presentation/page/post/post_analytics_screen.dart';
import 'package:flowkar/features/brand/presentation/page/create_brand.dart';
import 'package:flowkar/features/feedback_screen/presentation/page/feedback_screen.dart';

import 'package:flowkar/features/home_feed_screen/presentation/widget/comment_bottom_sheet_widget.dart';
import 'package:flowkar/features/live_stream/presentation/live_stream.dart';
import 'package:flowkar/features/onboarding/presentation/page/onboarding_main_screen.dart';
import 'package:flowkar/features/onboarding/presentation/page/onboarding_screen.dart';
import 'package:flowkar/features/profile_screen/presentation/page/edit_profile_screen.dart';
import 'package:flowkar/features/profile_screen/presentation/page/scheduled_post_screen.dart';
import 'package:flowkar/features/profile_screen/presentation/page/share_profile_screen.dart';

// import 'package:flowkar/features/discover/page/discover_page.dart';
import 'package:flowkar/features/notification/page/notification_screen.dart';

import 'package:flowkar/features/setting_screen/page/save_post/presentation/save_post_page.dart';
// import 'package:flowkar/features/setting_screen/page/setting_screen.dart';
import 'package:flowkar/features/sm_chat/presentation/flowkar_chat_screen.dart';
import 'package:flowkar/features/splash/presentation/no_internet_screen.dart';
import 'package:flowkar/features/subscription/presentation/page/subscription_plan_screen.dart';
import 'package:flowkar/features/sm_chat/presentation/sm_chat_detail_screen.dart';
import 'package:flowkar/features/sm_chat/presentation/sm_chat_screen.dart';
import 'package:flowkar/features/user_management/page/add_user_roll_screen.dart';
import 'package:flowkar/features/user_management/page/list_of_brand_screen.dart';
import 'package:flowkar/features/user_management/page/search_user_management_screen.dart';
import 'package:flowkar/features/user_management/page/user_management_screen.dart';
import 'package:flowkar/features/social_connect/presentation/page/anylitics_view.dart';
import 'package:flowkar/features/social_connect/presentation/widget/social_connect_web_view.dart';
import 'package:flowkar/features/upload_post/presentation/pages/edit_post_screen.dart';

import 'package:flowkar/features/story_module/New_story_code/new_story_code.dart';
import 'package:flowkar/features/upload_post/presentation/pages/upload_post_screen.dart';
import 'package:flowkar/features/upload_post/presentation/widget/video_image_editor.dart';
import 'package:flowkar/features/widgets/common/get_user_profile_by_id/get_user_profile_by_id.dart';

class AppRoutes {
  static const String initialRoute = '/splash_Screen';
  static const String onboardingmainPage = '/onboardingmainPage';
  static const String onboardingPage = '/onboarding_screen';
  static const String signinPage = '/signinpage';
  static const String signupPage = '/signupPage';
  static const String forgotPasswordPage = '/forgotPasswordPage';
  static const String verifyEmailPage = '/verifyEmailPage';
  static const String createNewPasswordPage = '/createNewPasswordPage';
  static const String uploadPostScrteen = '/upload_post_screen';
  static const String bottomNavBar = '/bottom_bar_screen';
  static const String socialConnect = '/social_connect_screen';
  static const String socialConnectwebview = '/social_connect_webview';
  static const String policywebview = '/policy_web_view';
  static const String resetPasswordSuccessScreen = '/reset_password_success_screen';
  static const String homeFeedScreen = '/home_feed_screen';
  static const String commentBottomSheet = '/comment_bottom_sheet';
  static const String discoverpage = '/discoverpage';
  static const String userProfileScreen = '/userProfileScreen';
  static const String shareProfileScreen = '/shareProfileScreen';
  static const String settingScreen = '/settingScreen';
  static const String editProfileScreen = '/editProfileScreen';
  static const String notificationpage = '/notificationpage';
  static const String savepostpage = '/save_post_page';
  static const String chatscreen = '/chatscreen';
  static const String flowkarChatscreen = '/flowkarChatscreen';
  static const String noInternetScreen = '/noInternetScreen';

  static const String scheduledpostscreen = '/scheduled_post_screen';
  static const String getUserProfilebyId = '/get_user_profile_by_id';
  static const String EditPostscreen = '/edit_post_screen';
  static const String anyliticsview = '/anylitics_view';
  static const String postanalyticsscreen = '/post_analytics_screen';
  static const String videoandimageEditorScreen = '/video_image_editor_screen';
  static const String newstoryscreen = '/new_story_code';
  static const String feedbackscreen = '/feedback_screen';
  static const String userTypeSelectionScreen = '/userTypeSelectionScreen';
  static const String brandregistrationscreen = '/brand_registration_screen';
  static const String userManagementScreen = '/user_management_screen';
  static const String searchUserManagementScreen = '/search_user_management_screen';
  static const String listOfBrandScreen = '/list_of_brand_screen';
  static const String addUserRollScreen = '/add_user_roll_screen';
  static const String listOfRolesScreen = '/list_of_roles_screen';
  static const String subscriptionPlanScreen = '/subscription_plan_screen';
  static const String socialDataListScreen = '/social_data_list_screen';
  static const String analyticsChartsScreen = '/analytics_charts_screen';
  static const String smchatScreen = '/sm_chat_screen';
  static const String smchatdetailScreen = '/sm_chat_detail_screen';
  static const String brandListScreen = '/brand_list_screen';
  static const String createBrandScreen = '/create_brand_screen';
  static const String deepLinkPost = '/deep_link_post';
  static const String reelsScreen = '/reels_screen';
  static const String livestreamScreen = '/live_stream_screen';

  static Map<String, WidgetBuilder> get routes => {
        initialRoute: SplashScreen.builder,
        onboardingmainPage: OnboardingmainScreen.builder,
        onboardingPage: OnboardingScreen.builder,
        signinPage: SigninPage.builder,
        signupPage: SignUpPage.builder,
        forgotPasswordPage: ForgotPasswordPage.builder,
        verifyEmailPage: VerifyEmailPage.builder,
        createNewPasswordPage: CreateNewPasswordPage.builder,
        uploadPostScrteen: UploadPostScreen.builder,
        bottomNavBar: BottomNavBar.builder,
        socialConnect: SocialConnectPage.builder,
        socialConnectwebview: SocialConnectWebView.builder,
        policywebview: PolicyWebView.builder,
        // socialConnectwebview: SocialConnectWebView.builder,
        resetPasswordSuccessScreen: ResetPasswordSuccessScreen.builder,
        homeFeedScreen: HomeFeedScreen.builder,
        commentBottomSheet: CommentsBottomSheet.builder,
        // discoverpage: DiscoverPage.builder,
        userProfileScreen: UserProfileScreen.builder,
        shareProfileScreen: ShareProfileScreen.builder,
        // settingScreen: SettingScreen.builder,

        editProfileScreen: EditProfileScreen.builder,
        notificationpage: NotificationPage.builder,
        savepostpage: SavePostPage.builder,
        // chatscreen: ChatScreen.builder,
        flowkarChatscreen: FlowkarChatScreen.builder,

        scheduledpostscreen: ScheduledPostScreen.builder,
        getUserProfilebyId: GetUserProfileById.builder,
        EditPostscreen: EditPostScreenState.builder,
        anyliticsview: Anyliticsview.builder,
        postanalyticsscreen: PostAnalyticsScreen.builder,
        videoandimageEditorScreen: VideoAndImageEditorScreen.builder,

        newstoryscreen: NewStoryScreen.builder,
        feedbackscreen: FeedbackScreen.builder,
        userTypeSelectionScreen: UserTypeSelectionScreen.builder,
        brandregistrationscreen: BrandRegistrationScreen.builder,
        userManagementScreen: UserManagementScreen.builder,
        searchUserManagementScreen: SearchUserManagementScreen.builder,
        listOfBrandScreen: LostOfBrandScreen.builder,
        addUserRollScreen: AddUserListScreen.builder,
        subscriptionPlanScreen: SubscriptionPlanScreen.builder,
        socialDataListScreen: SocialDataList.builder,
        analyticsChartsScreen: AnalyticsCharts.builder,
        noInternetScreen: NoInternetScreen.builder,
        // listOfRolesScreen: ListOfRolesScreen.builder,
        smchatScreen: SmChatScreen.builder,
        smchatdetailScreen: SmChatDetailScreen.builder,
        brandListScreen: BrandListScreen.builder,
        createBrandScreen: CreateBrandScreen.builder,
        // reelsScreen: ReelsScreen.builder,
        livestreamScreen: LiveStreamPage.builder,
        // deepLinkPost: DeepLinkPostScreen.builder,
      };
}
