import 'package:flutter/material.dart';
import 'package:flowkar/core/utils/exports.dart';

/// Widget that displays content progressively as it loads
class ProgressiveLoadingWidget<T> extends StatefulWidget {
  final List<T> items;
  final bool isLoading;
  final bool hasMore;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final Widget Function(BuildContext context)? loadingBuilder;
  final Widget Function(BuildContext context)? emptyBuilder;
  final VoidCallback? onLoadMore;
  final ScrollController? scrollController;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final int? itemCount;

  const ProgressiveLoadingWidget({
    Key? key,
    required this.items,
    required this.itemBuilder,
    this.isLoading = false,
    this.hasMore = true,
    this.loadingBuilder,
    this.emptyBuilder,
    this.onLoadMore,
    this.scrollController,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.itemCount,
  }) : super(key: key);

  @override
  State<ProgressiveLoadingWidget<T>> createState() => _ProgressiveLoadingWidgetState<T>();
}

class _ProgressiveLoadingWidgetState<T> extends State<ProgressiveLoadingWidget<T>> with TickerProviderStateMixin {
  late ScrollController _scrollController;
  late AnimationController _fadeController;
  final Set<int> _animatedIndices = {};

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scrollController.addListener(_onScroll);
    _fadeController.forward();
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    _fadeController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8) {
      if (widget.hasMore && !widget.isLoading && widget.onLoadMore != null) {
        widget.onLoadMore!();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.items.isEmpty && !widget.isLoading) {
      return widget.emptyBuilder?.call(context) ?? const Center(child: Text('No data available'));
    }

    final totalItems = widget.itemCount ?? widget.items.length;
    final showLoadingIndicator = widget.isLoading && widget.hasMore;

    return ListView.builder(
      controller: _scrollController,
      padding: widget.padding,
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      itemCount: totalItems + (showLoadingIndicator ? 1 : 0),
      itemBuilder: (context, index) {
        // Show loading indicator at the end
        if (index >= totalItems) {
          return widget.loadingBuilder?.call(context) ??
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: CircularProgressIndicator(),
                ),
              );
        }

        // Show actual item with progressive animation
        if (index < widget.items.length) {
          return _buildAnimatedItem(context, widget.items[index], index);
        }

        // Show shimmer for items that haven't loaded yet
        return _buildShimmerItem(context, index);
      },
    );
  }

  Widget _buildAnimatedItem(BuildContext context, T item, int index) {
    // Animate new items as they appear
    if (!_animatedIndices.contains(index)) {
      _animatedIndices.add(index);

      return TweenAnimationBuilder<double>(
        duration: Duration(milliseconds: 300 + (index % 3) * 100),
        tween: Tween(begin: 0.0, end: 1.0),
        curve: Curves.easeOutBack,
        builder: (context, value, child) {
          return Transform.translate(
            offset: Offset(0, 20 * (1 - value)),
            child: Opacity(
              opacity: value,
              child: widget.itemBuilder(context, item, index),
            ),
          );
        },
      );
    }

    return widget.itemBuilder(context, item, index);
  }

  Widget _buildShimmerItem(BuildContext context, int index) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildShimmerBox(double.infinity, 20),
              const SizedBox(height: 8),
              _buildShimmerBox(200, 16),
              const SizedBox(height: 8),
              _buildShimmerBox(150, 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerBox(double width, double height) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1000),
      tween: Tween(begin: 0.3, end: 1.0),
      curve: Curves.easeInOut,
      builder: (context, value, child) {
        return Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: Colors.grey[300]?.withValues(alpha: value),
            borderRadius: BorderRadius.circular(4),
          ),
        );
      },
    );
  }
}

/// Mixin for easy progressive loading integration in screens
mixin ProgressiveLoadingUIMixin<T extends StatefulWidget> on State<T> {
  final ScrollController progressiveScrollController = ScrollController();

  @override
  void dispose() {
    progressiveScrollController.dispose();
    super.dispose();
  }

  /// Build a progressive loading list
  Widget buildProgressiveList<ItemType>({
    required List<ItemType> items,
    required Widget Function(BuildContext, ItemType, int) itemBuilder,
    required bool isLoading,
    required bool hasMore,
    required VoidCallback onLoadMore,
    Widget Function(BuildContext)? loadingBuilder,
    Widget Function(BuildContext)? emptyBuilder,
    EdgeInsets? padding,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
  }) {
    return ProgressiveLoadingWidget<ItemType>(
      items: items,
      itemBuilder: itemBuilder,
      isLoading: isLoading,
      hasMore: hasMore,
      onLoadMore: onLoadMore,
      scrollController: progressiveScrollController,
      loadingBuilder: loadingBuilder,
      emptyBuilder: emptyBuilder,
      padding: padding,
      shrinkWrap: shrinkWrap,
      physics: physics,
    );
  }
}

/// Progressive loading indicator widget
class ProgressiveLoadingIndicator extends StatefulWidget {
  final String? message;
  final double? progress;
  final bool showProgress;

  const ProgressiveLoadingIndicator({
    Key? key,
    this.message,
    this.progress,
    this.showProgress = false,
  }) : super(key: key);

  @override
  State<ProgressiveLoadingIndicator> createState() => _ProgressiveLoadingIndicatorState();
}

class _ProgressiveLoadingIndicatorState extends State<ProgressiveLoadingIndicator> with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();
    _animation = Tween<double>(begin: 0, end: 1).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.showProgress && widget.progress != null)
            LinearProgressIndicator(value: widget.progress)
          else
            AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return CircularProgressIndicator(
                  value: _animation.value,
                );
              },
            ),
          if (widget.message != null) ...[
            const SizedBox(height: 8),
            Text(
              widget.message!,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// Staggered animation for progressive loading
class StaggeredProgressiveAnimation extends StatefulWidget {
  final List<Widget> children;
  final Duration delay;
  final Duration duration;

  const StaggeredProgressiveAnimation({
    Key? key,
    required this.children,
    this.delay = const Duration(milliseconds: 100),
    this.duration = const Duration(milliseconds: 300),
  }) : super(key: key);

  @override
  State<StaggeredProgressiveAnimation> createState() => _StaggeredProgressiveAnimationState();
}

class _StaggeredProgressiveAnimationState extends State<StaggeredProgressiveAnimation> with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(
      widget.children.length,
      (index) => AnimationController(duration: widget.duration, vsync: this),
    );
    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeOut),
      );
    }).toList();

    _startAnimations();
  }

  void _startAnimations() async {
    for (int i = 0; i < _controllers.length; i++) {
      await Future.delayed(widget.delay);
      if (mounted) {
        _controllers[i].forward();
      }
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(widget.children.length, (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, 20 * (1 - _animations[index].value)),
              child: Opacity(
                opacity: _animations[index].value,
                child: widget.children[index],
              ),
            );
          },
        );
      }),
    );
  }
}
